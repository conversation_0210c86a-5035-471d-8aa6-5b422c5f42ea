#!/usr/bin/env python3
"""
Bug Fixes and Improvements for Psychiatric Assessment System
This file contains specific fixes for identified issues
"""

import streamlit as st
import sqlite3
import datetime
import json
import logging
from typing import Optional, Dict, Any, List, Tuple
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('psychiatric_assessment.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """Improved database manager with better error handling and connection pooling"""
    
    def __init__(self, db_path: str = 'psychiatric_assessments.db'):
        self.db_path = db_path
        self._init_database()
    
    def _init_database(self):
        """Initialize database with proper error handling"""
        try:
            conn = self.get_connection()
            if conn:
                self._create_tables(conn)
                conn.close()
                logger.info("Database initialized successfully")
        except Exception as e:
            logger.error(f"Database initialization failed: {e}")
            raise
    
    def get_connection(self) -> Optional[sqlite3.Connection]:
        """Get database connection with improved error handling"""
        try:
            conn = sqlite3.connect(
                self.db_path,
                timeout=30.0,  # 30 second timeout
                check_same_thread=False
            )
            conn.execute("PRAGMA foreign_keys = ON")  # Enable foreign key constraints
            conn.execute("PRAGMA journal_mode = WAL")  # Better concurrency
            return conn
        except sqlite3.Error as e:
            logger.error(f"Database connection failed: {e}")
            st.error(f"Database connection failed: {e}")
            return None
    
    def _create_tables(self, conn: sqlite3.Connection):
        """Create tables with improved schema"""
        cursor = conn.cursor()
        
        # Enhanced patients table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS patients (
            patient_id TEXT PRIMARY KEY,
            age INTEGER CHECK (age >= 0 AND age <= 150),
            gender TEXT,
            sex_assigned TEXT,
            marital_status TEXT,
            children TEXT,
            education TEXT,
            occupation TEXT,
            employment_status TEXT,
            ethnicity TEXT,
            living_situation TEXT,
            housing_stability TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Enhanced assessments table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS assessments (
            assessment_id INTEGER PRIMARY KEY AUTOINCREMENT,
            patient_id TEXT NOT NULL,
            assessment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            chief_complaint TEXT,
            history_present_illness TEXT,
            past_psychiatric_history TEXT,
            past_medical_history TEXT,
            family_history TEXT,
            social_developmental_history TEXT,
            substance_use TEXT,
            mental_state_examination TEXT,
            cognitive_assessment TEXT,
            risk_assessment TEXT,
            laboratory_investigations TEXT,
            clinical_scales TEXT,
            diagnostic_formulation TEXT,
            treatment_planning TEXT,
            follow_up_monitoring TEXT,
            suicide_risk_level TEXT CHECK (suicide_risk_level IN ('Low', 'Moderate', 'High', 'Imminent')),
            primary_diagnosis TEXT,
            phq9_score INTEGER CHECK (phq9_score >= 0 AND phq9_score <= 27),
            gad7_score INTEGER CHECK (gad7_score >= 0 AND gad7_score <= 21),
            duration_minutes REAL CHECK (duration_minutes >= 0),
            completion_percentage REAL CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
            data_json TEXT,
            created_by TEXT DEFAULT 'system',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (patient_id) REFERENCES patients (patient_id) ON DELETE CASCADE
        )
        ''')
        
        # Create indexes for better performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_patient_id ON assessments (patient_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_assessment_date ON assessments (assessment_date)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_primary_diagnosis ON assessments (primary_diagnosis)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_suicide_risk ON assessments (suicide_risk_level)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_completion ON assessments (completion_percentage)')
        
        # Create audit log table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS audit_log (
            log_id INTEGER PRIMARY KEY AUTOINCREMENT,
            table_name TEXT NOT NULL,
            record_id TEXT NOT NULL,
            action TEXT NOT NULL CHECK (action IN ('INSERT', 'UPDATE', 'DELETE')),
            old_values TEXT,
            new_values TEXT,
            user_id TEXT DEFAULT 'system',
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        conn.commit()

class ValidationManager:
    """Improved validation with comprehensive error checking"""
    
    @staticmethod
    def validate_age(age: Optional[int]) -> Tuple[bool, str]:
        """Validate age with proper error messages"""
        if age is None:
            return False, "Age is required"
        if not isinstance(age, int):
            return False, "Age must be a number"
        if age < 0 or age > 150:
            return False, "Age must be between 0 and 150"
        return True, ""
    
    @staticmethod
    def validate_phq9_score(scores: Dict[str, int]) -> Tuple[bool, str, int]:
        """Validate and calculate PHQ-9 score"""
        try:
            total = 0
            for i in range(1, 10):  # PHQ-9 has 9 questions
                score = scores.get(f'q{i}', 0)
                if not isinstance(score, int) or score < 0 or score > 3:
                    return False, f"Question {i} score must be between 0 and 3", 0
                total += score
            
            if total < 0 or total > 27:
                return False, "Total PHQ-9 score must be between 0 and 27", 0
            
            return True, "", total
        except Exception as e:
            return False, f"Error calculating PHQ-9 score: {e}", 0
    
    @staticmethod
    def validate_gad7_score(scores: Dict[str, int]) -> Tuple[bool, str, int]:
        """Validate and calculate GAD-7 score"""
        try:
            total = 0
            for i in range(1, 8):  # GAD-7 has 7 questions
                score = scores.get(f'q{i}', 0)
                if not isinstance(score, int) or score < 0 or score > 3:
                    return False, f"Question {i} score must be between 0 and 3", 0
                total += score
            
            if total < 0 or total > 21:
                return False, "Total GAD-7 score must be between 0 and 21", 0
            
            return True, "", total
        except Exception as e:
            return False, f"Error calculating GAD-7 score: {e}", 0
    
    @staticmethod
    def validate_patient_id(patient_id: str) -> Tuple[bool, str]:
        """Validate patient ID format"""
        if not patient_id or not patient_id.strip():
            return False, "Patient ID is required"
        
        patient_id = patient_id.strip()
        if len(patient_id) < 3:
            return False, "Patient ID must be at least 3 characters"
        if len(patient_id) > 50:
            return False, "Patient ID must be less than 50 characters"
        
        # Check for valid characters (alphanumeric, hyphens, underscores)
        import re
        if not re.match(r'^[A-Za-z0-9_-]+$', patient_id):
            return False, "Patient ID can only contain letters, numbers, hyphens, and underscores"
        
        return True, ""

class RiskAssessmentManager:
    """Improved risk assessment with better logic"""
    
    @staticmethod
    def calculate_suicide_risk_level(risk_data: Dict[str, Any]) -> str:
        """Calculate suicide risk level with improved logic"""
        try:
            suicide_data = risk_data.get('suicide', {})
            
            # Initialize risk score
            risk_score = 0
            
            # Current suicidal ideation (highest weight)
            current_si = suicide_data.get('current_si', 'None')
            si_scores = {
                'None': 0,
                'Passive (wish to be dead)': 1,
                'Active ideation without plan': 2,
                'Active ideation with plan': 3,
                'Active ideation with intent': 4
            }
            risk_score += si_scores.get(current_si, 0) * 3
            
            # Previous attempts
            previous_attempts = suicide_data.get('previous_attempts', 'None')
            if previous_attempts != 'None':
                risk_score += 2
            
            # Means access
            means_access = suicide_data.get('means_access', 'No access')
            if means_access == 'Immediate access':
                risk_score += 2
            elif means_access == 'Some access':
                risk_score += 1
            
            # Family history
            family_suicide = suicide_data.get('family_suicide', 'No')
            if family_suicide == 'Yes - completed':
                risk_score += 2
            elif family_suicide == 'Yes - attempted':
                risk_score += 1
            
            # Additional risk factors
            if suicide_data.get('substance_use_current', False):
                risk_score += 1
            if suicide_data.get('social_isolation', False):
                risk_score += 1
            if suicide_data.get('recent_loss', False):
                risk_score += 1
            
            # Convert score to risk level
            if risk_score >= 12:
                return 'Imminent'
            elif risk_score >= 8:
                return 'High'
            elif risk_score >= 4:
                return 'Moderate'
            else:
                return 'Low'
                
        except Exception as e:
            logger.error(f"Error calculating suicide risk: {e}")
            return 'Moderate'  # Default to moderate risk on error

class SecurityManager:
    """Basic security measures"""
    
    @staticmethod
    def sanitize_input(text: str) -> str:
        """Sanitize text input to prevent basic injection attacks"""
        if not isinstance(text, str):
            return ""
        
        # Remove potentially dangerous characters
        import re
        # Remove script tags and SQL injection patterns
        text = re.sub(r'<script.*?</script>', '', text, flags=re.IGNORECASE | re.DOTALL)
        text = re.sub(r'(union|select|insert|update|delete|drop|create|alter)\s', '', text, flags=re.IGNORECASE)
        
        return text.strip()
    
    @staticmethod
    def hash_patient_id(patient_id: str) -> str:
        """Hash patient ID for additional privacy"""
        import hashlib
        return hashlib.sha256(patient_id.encode()).hexdigest()[:16]

class ErrorHandler:
    """Centralized error handling"""
    
    @staticmethod
    def handle_database_error(error: Exception, operation: str):
        """Handle database errors consistently"""
        error_msg = f"Database error during {operation}: {str(error)}"
        logger.error(error_msg)
        st.error(f"Database operation failed. Please try again or contact support.")
        return False
    
    @staticmethod
    def handle_validation_error(error: str, field: str):
        """Handle validation errors"""
        logger.warning(f"Validation error in {field}: {error}")
        st.error(f"Validation error in {field}: {error}")
        return False
    
    @staticmethod
    def handle_general_error(error: Exception, context: str):
        """Handle general errors"""
        error_msg = f"Error in {context}: {str(error)}"
        logger.error(error_msg)
        st.error(f"An error occurred. Please try again or contact support.")
        return False

# Configuration improvements
class Config:
    """Application configuration"""
    
    # Database settings
    DATABASE_PATH = os.getenv('DB_PATH', 'psychiatric_assessments.db')
    DATABASE_TIMEOUT = int(os.getenv('DB_TIMEOUT', '30'))
    
    # Security settings
    ENABLE_AUDIT_LOG = os.getenv('ENABLE_AUDIT_LOG', 'true').lower() == 'true'
    MAX_SESSION_DURATION = int(os.getenv('MAX_SESSION_DURATION', '3600'))  # 1 hour
    
    # Performance settings
    AUTO_SAVE_INTERVAL = int(os.getenv('AUTO_SAVE_INTERVAL', '60'))  # 60 seconds
    MAX_CONCURRENT_USERS = int(os.getenv('MAX_CONCURRENT_USERS', '50'))
    
    # UI settings
    DEFAULT_PORT = int(os.getenv('STREAMLIT_PORT', '8501'))
    ENABLE_WIDE_MODE = os.getenv('ENABLE_WIDE_MODE', 'true').lower() == 'true'

# Example usage of improvements
def improved_save_assessment_data(assessment_data: Dict[str, Any], patient_id: str) -> bool:
    """Improved assessment data saving with better error handling"""
    try:
        # Validate patient ID
        valid, error_msg = ValidationManager.validate_patient_id(patient_id)
        if not valid:
            return ErrorHandler.handle_validation_error(error_msg, "Patient ID")
        
        # Validate clinical scales if present
        if 'clinical_scales' in assessment_data:
            scales = assessment_data['clinical_scales']
            
            if 'depression' in scales:
                valid, error_msg, phq9_total = ValidationManager.validate_phq9_score(scales['depression'])
                if not valid:
                    return ErrorHandler.handle_validation_error(error_msg, "PHQ-9")
                scales['depression']['phq9_total'] = phq9_total
            
            if 'anxiety' in scales:
                valid, error_msg, gad7_total = ValidationManager.validate_gad7_score(scales['anxiety'])
                if not valid:
                    return ErrorHandler.handle_validation_error(error_msg, "GAD-7")
                scales['anxiety']['gad7_total'] = gad7_total
        
        # Calculate risk level
        risk_level = 'Low'
        if 'risk_assessment' in assessment_data:
            risk_level = RiskAssessmentManager.calculate_suicide_risk_level(assessment_data['risk_assessment'])
        
        # Save to database with improved error handling
        db_manager = DatabaseManager()
        conn = db_manager.get_connection()
        if not conn:
            return False
        
        try:
            cursor = conn.cursor()
            
            # Insert assessment with proper error handling
            cursor.execute('''
            INSERT INTO assessments (
                patient_id, suicide_risk_level, primary_diagnosis,
                phq9_score, gad7_score, data_json
            ) VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                patient_id,
                risk_level,
                assessment_data.get('diagnostic_formulation', {}).get('primary_diagnosis'),
                assessment_data.get('clinical_scales', {}).get('depression', {}).get('phq9_total'),
                assessment_data.get('clinical_scales', {}).get('anxiety', {}).get('gad7_total'),
                json.dumps(assessment_data, cls=DateTimeEncoder)
            ))
            
            conn.commit()
            logger.info(f"Assessment saved successfully for patient {patient_id}")
            return True
            
        except sqlite3.Error as e:
            conn.rollback()
            return ErrorHandler.handle_database_error(e, "save assessment")
        finally:
            conn.close()
            
    except Exception as e:
        return ErrorHandler.handle_general_error(e, "save assessment data")

# Custom JSON encoder (improved version)
class DateTimeEncoder(json.JSONEncoder):
    """Improved JSON encoder with better error handling"""
    def default(self, obj):
        try:
            if isinstance(obj, (datetime.datetime, datetime.date)):
                return obj.isoformat()
            elif isinstance(obj, datetime.time):
                return obj.isoformat()
            elif hasattr(obj, '__dict__'):
                return obj.__dict__
            return super().default(obj)
        except Exception as e:
            logger.warning(f"JSON encoding error for object {type(obj)}: {e}")
            return str(obj)
