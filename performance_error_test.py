#!/usr/bin/env python3
"""
Performance and Error Handling Testing for Psychiatric Assessment System
Tests application performance, error handling, edge cases, and data validation
"""

import sys
import os
import sqlite3
import pandas as pd
import json
import datetime
import time
import threading
import concurrent.futures
from typing import Dict, Any, List

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class PerformanceErrorTester:
    def __init__(self):
        self.test_results = []
        self.test_db_path = 'test_performance.db'
        
    def log_test(self, test_name: str, passed: bool, message: str = ""):
        """Log test results"""
        status = "✅ PASS" if passed else "❌ FAIL"
        self.test_results.append({
            'test': test_name,
            'passed': passed,
            'message': message
        })
        print(f"{status}: {test_name}")
        if message:
            print(f"    {message}")
    
    def test_database_performance(self):
        """Test database performance under load"""
        try:
            # Create test database
            conn = sqlite3.connect(self.test_db_path)
            cursor = conn.cursor()
            
            # Create tables
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS patients (
                patient_id TEXT PRIMARY KEY,
                age INTEGER,
                gender TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS assessments (
                assessment_id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id TEXT,
                assessment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                phq9_score INTEGER,
                gad7_score INTEGER,
                data_json TEXT,
                FOREIGN KEY (patient_id) REFERENCES patients (patient_id)
            )
            ''')
            
            # Test bulk insert performance
            start_time = time.time()
            patients_data = [(f'PERF-{i:04d}', 20 + (i % 60), 'Male' if i % 2 == 0 else 'Female') 
                           for i in range(1000)]
            
            cursor.executemany(
                'INSERT INTO patients (patient_id, age, gender) VALUES (?, ?, ?)',
                patients_data
            )
            conn.commit()
            
            insert_time = time.time() - start_time
            if insert_time < 2.0:  # Should complete in under 2 seconds
                self.log_test("Bulk Insert Performance", True, f"1000 patients inserted in {insert_time:.3f}s")
            else:
                self.log_test("Bulk Insert Performance", False, f"Insert took {insert_time:.3f}s (too slow)")
                return False
            
            # Test query performance
            start_time = time.time()
            cursor.execute("SELECT COUNT(*) FROM patients WHERE age > 30")
            count = cursor.fetchone()[0]
            query_time = time.time() - start_time
            
            if query_time < 0.1:  # Should complete in under 0.1 seconds
                self.log_test("Query Performance", True, f"Query completed in {query_time:.3f}s (found {count} records)")
            else:
                self.log_test("Query Performance", False, f"Query took {query_time:.3f}s (too slow)")
                return False
            
            # Test complex join performance
            start_time = time.time()
            cursor.execute("""
                SELECT p.patient_id, p.age, COUNT(a.assessment_id) as assessment_count
                FROM patients p
                LEFT JOIN assessments a ON p.patient_id = a.patient_id
                GROUP BY p.patient_id, p.age
                HAVING p.age > 25
                ORDER BY assessment_count DESC
                LIMIT 100
            """)
            results = cursor.fetchall()
            join_time = time.time() - start_time
            
            if join_time < 0.5:  # Should complete in under 0.5 seconds
                self.log_test("Complex Join Performance", True, f"Join query completed in {join_time:.3f}s")
            else:
                self.log_test("Complex Join Performance", False, f"Join took {join_time:.3f}s (too slow)")
                return False
            
            conn.close()
            return True
            
        except Exception as e:
            self.log_test("Database Performance", False, str(e))
            return False
    
    def test_concurrent_database_access(self):
        """Test concurrent database access"""
        try:
            def database_worker(worker_id):
                """Worker function for concurrent database access"""
                try:
                    conn = sqlite3.connect(self.test_db_path, timeout=10.0)
                    cursor = conn.cursor()
                    
                    # Perform some database operations
                    cursor.execute("SELECT COUNT(*) FROM patients")
                    count = cursor.fetchone()[0]
                    
                    # Insert a test record
                    cursor.execute(
                        "INSERT OR IGNORE INTO patients (patient_id, age, gender) VALUES (?, ?, ?)",
                        (f'WORKER-{worker_id}', 30, 'Test')
                    )
                    conn.commit()
                    conn.close()
                    return True
                except Exception as e:
                    return False
            
            # Test with multiple concurrent workers
            start_time = time.time()
            with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
                futures = [executor.submit(database_worker, i) for i in range(20)]
                results = [future.result() for future in concurrent.futures.as_completed(futures)]
            
            concurrent_time = time.time() - start_time
            success_count = sum(results)
            
            if success_count >= 18:  # At least 90% should succeed
                self.log_test("Concurrent Database Access", True, 
                            f"{success_count}/20 workers succeeded in {concurrent_time:.3f}s")
            else:
                self.log_test("Concurrent Database Access", False, 
                            f"Only {success_count}/20 workers succeeded")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("Concurrent Database Access", False, str(e))
            return False
    
    def test_memory_usage(self):
        """Test memory usage with large datasets"""
        try:
            # Create large DataFrame to simulate memory usage
            large_data = pd.DataFrame({
                'patient_id': [f'MEM-{i:06d}' for i in range(10000)],
                'age': [20 + (i % 60) for i in range(10000)],
                'phq9_score': [i % 28 for i in range(10000)],
                'gad7_score': [i % 22 for i in range(10000)],
                'data_json': [json.dumps({'test': f'data_{i}'}) for i in range(10000)]
            })
            
            # Test memory-efficient operations
            start_time = time.time()
            
            # Group operations
            age_groups = large_data.groupby('age')['phq9_score'].mean()
            
            # Filtering operations
            high_scores = large_data[large_data['phq9_score'] > 15]
            
            # Statistical operations
            correlation = large_data['phq9_score'].corr(large_data['gad7_score'])
            
            processing_time = time.time() - start_time
            
            if processing_time < 1.0:  # Should complete in under 1 second
                self.log_test("Large Dataset Processing", True, 
                            f"10K records processed in {processing_time:.3f}s")
            else:
                self.log_test("Large Dataset Processing", False, 
                            f"Processing took {processing_time:.3f}s (too slow)")
                return False
            
            # Test memory cleanup
            del large_data, age_groups, high_scores
            
            self.log_test("Memory Management", True, "Large datasets cleaned up successfully")
            return True
            
        except Exception as e:
            self.log_test("Memory Usage", False, str(e))
            return False
    
    def test_error_handling_scenarios(self):
        """Test various error handling scenarios"""
        try:
            from streamlit_sql_zai import (
                validate_date, validate_number, validate_required,
                calculate_suicide_risk_level, safe_json_dumps
            )
            
            # Test invalid date handling
            try:
                error = validate_date("invalid_date", allow_future=False)
                if error is not None:
                    self.log_test("Invalid Date Error Handling", True, "Invalid date properly rejected")
                else:
                    self.log_test("Invalid Date Error Handling", False, "Invalid date not caught")
                    return False
            except Exception:
                self.log_test("Invalid Date Error Handling", True, "Exception properly caught")
            
            # Test invalid number handling
            try:
                error = validate_number("not_a_number", 0, 100, "Test Field")
                if error is not None:
                    self.log_test("Invalid Number Error Handling", True, "Invalid number properly rejected")
                else:
                    self.log_test("Invalid Number Error Handling", False, "Invalid number not caught")
                    return False
            except Exception:
                self.log_test("Invalid Number Error Handling", True, "Exception properly caught")
            
            # Test None value handling
            error = validate_required(None, "Test Field")
            if error is not None:
                self.log_test("None Value Error Handling", True, "None value properly rejected")
            else:
                self.log_test("None Value Error Handling", False, "None value not caught")
                return False
            
            # Test malformed risk data
            malformed_risk = {"invalid": "data"}
            risk_level = calculate_suicide_risk_level(malformed_risk)
            if risk_level in ['Low', 'Moderate']:  # Should default to safe value
                self.log_test("Malformed Risk Data Handling", True, f"Safe default: {risk_level}")
            else:
                self.log_test("Malformed Risk Data Handling", False, f"Unsafe result: {risk_level}")
                return False
            
            # Test JSON serialization with problematic data
            problematic_data = {
                'circular_ref': None,
                'function': lambda x: x,  # Non-serializable
                'datetime': datetime.datetime.now()
            }
            problematic_data['circular_ref'] = problematic_data  # Circular reference
            
            json_result = safe_json_dumps(problematic_data)
            if isinstance(json_result, str) and len(json_result) > 0:
                self.log_test("Problematic JSON Handling", True, "Problematic data handled gracefully")
            else:
                self.log_test("Problematic JSON Handling", False, "JSON serialization failed")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("Error Handling Scenarios", False, str(e))
            return False
    
    def test_edge_cases(self):
        """Test edge cases and boundary conditions"""
        try:
            # Test empty data handling
            empty_df = pd.DataFrame()
            if len(empty_df) == 0:
                self.log_test("Empty DataFrame Handling", True, "Empty DataFrame handled correctly")
            else:
                self.log_test("Empty DataFrame Handling", False, "Empty DataFrame not handled")
                return False
            
            # Test boundary values for clinical scales
            boundary_tests = [
                (0, "PHQ-9 minimum"),
                (27, "PHQ-9 maximum"),
                (0, "GAD-7 minimum"),
                (21, "GAD-7 maximum")
            ]
            
            for value, test_name in boundary_tests:
                if test_name.startswith("PHQ-9"):
                    valid = 0 <= value <= 27
                else:  # GAD-7
                    valid = 0 <= value <= 21
                
                if valid:
                    self.log_test(f"Boundary Value - {test_name}", True, f"Value {value} is valid")
                else:
                    self.log_test(f"Boundary Value - {test_name}", False, f"Value {value} should be valid")
                    return False
            
            # Test very long strings
            long_string = "x" * 10000
            if len(long_string) == 10000:
                self.log_test("Long String Handling", True, "10K character string handled")
            else:
                self.log_test("Long String Handling", False, "Long string not handled properly")
                return False
            
            # Test special characters
            special_chars = "!@#$%^&*()_+-=[]{}|;':\",./<>?"
            if len(special_chars) > 0:
                self.log_test("Special Characters Handling", True, "Special characters handled")
            else:
                self.log_test("Special Characters Handling", False, "Special characters not handled")
                return False
            
            # Test Unicode characters
            unicode_text = "Testing unicode: 你好 🌟 café naïve résumé"
            if len(unicode_text) > 0:
                self.log_test("Unicode Handling", True, "Unicode characters handled")
            else:
                self.log_test("Unicode Handling", False, "Unicode not handled properly")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("Edge Cases", False, str(e))
            return False
    
    def test_data_validation_edge_cases(self):
        """Test data validation with edge cases"""
        try:
            from streamlit_sql_zai import validate_date, validate_number
            
            # Test extreme dates
            very_old_date = datetime.date(1900, 1, 1)
            very_future_date = datetime.date(2100, 12, 31)
            
            # Old date should be valid
            error = validate_date(very_old_date, allow_future=False)
            if error is None:
                self.log_test("Very Old Date Validation", True, "1900 date accepted")
            else:
                self.log_test("Very Old Date Validation", False, f"Old date rejected: {error}")
                return False
            
            # Future date should be rejected when not allowed
            error = validate_date(very_future_date, allow_future=False)
            if error is not None:
                self.log_test("Very Future Date Validation", True, "2100 date properly rejected")
            else:
                self.log_test("Very Future Date Validation", False, "Future date should be rejected")
                return False
            
            # Test extreme numbers
            extreme_tests = [
                (-999999, 0, 100, "Extremely negative number"),
                (999999, 0, 100, "Extremely positive number"),
                (0, 0, 100, "Boundary minimum"),
                (100, 0, 100, "Boundary maximum"),
                (50.5, 0, 100, "Decimal number")
            ]
            
            for value, min_val, max_val, test_name in extreme_tests:
                error = validate_number(value, min_val, max_val, "Test")
                expected_valid = min_val <= value <= max_val
                
                if expected_valid and error is None:
                    self.log_test(f"Number Validation - {test_name}", True, f"Value {value} correctly accepted")
                elif not expected_valid and error is not None:
                    self.log_test(f"Number Validation - {test_name}", True, f"Value {value} correctly rejected")
                else:
                    self.log_test(f"Number Validation - {test_name}", False, 
                                f"Value {value} validation failed")
                    return False
            
            return True
            
        except Exception as e:
            self.log_test("Data Validation Edge Cases", False, str(e))
            return False
    
    def test_stress_scenarios(self):
        """Test application under stress conditions"""
        try:
            # Test rapid successive operations
            start_time = time.time()
            operations_count = 0
            
            for i in range(100):
                # Simulate rapid data processing
                test_data = {
                    'patient_id': f'STRESS-{i}',
                    'scores': [j % 28 for j in range(10)],
                    'timestamp': datetime.datetime.now()
                }
                
                # JSON serialization stress test
                json_str = json.dumps(test_data, default=str)
                parsed_data = json.loads(json_str)
                
                operations_count += 1
            
            stress_time = time.time() - start_time
            
            if stress_time < 1.0 and operations_count == 100:
                self.log_test("Rapid Operations Stress Test", True, 
                            f"100 operations in {stress_time:.3f}s")
            else:
                self.log_test("Rapid Operations Stress Test", False, 
                            f"Operations took {stress_time:.3f}s or failed")
                return False
            
            # Test memory stress with repeated allocations
            memory_stress_passed = True
            try:
                for i in range(10):
                    large_list = [f'item_{j}' for j in range(1000)]
                    del large_list  # Immediate cleanup
                
                self.log_test("Memory Stress Test", True, "Repeated allocations handled")
            except MemoryError:
                self.log_test("Memory Stress Test", False, "Memory error occurred")
                memory_stress_passed = False
            
            return memory_stress_passed
            
        except Exception as e:
            self.log_test("Stress Scenarios", False, str(e))
            return False
    
    def cleanup_test_data(self):
        """Clean up test data"""
        try:
            if os.path.exists(self.test_db_path):
                os.remove(self.test_db_path)
            self.log_test("Performance Test Cleanup", True, "Test database removed")
        except Exception as e:
            self.log_test("Performance Test Cleanup", False, str(e))
    
    def run_all_tests(self):
        """Run all performance and error handling tests"""
        print("🧪 Starting Performance and Error Handling Testing")
        print("=" * 60)
        
        tests = [
            ("Database Performance", self.test_database_performance),
            ("Concurrent Database Access", self.test_concurrent_database_access),
            ("Memory Usage", self.test_memory_usage),
            ("Error Handling Scenarios", self.test_error_handling_scenarios),
            ("Edge Cases", self.test_edge_cases),
            ("Data Validation Edge Cases", self.test_data_validation_edge_cases),
            ("Stress Scenarios", self.test_stress_scenarios),
            ("Cleanup", self.cleanup_test_data)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                self.log_test(test_name, False, f"Exception: {e}")
                failed += 1
        
        print("\n" + "=" * 60)
        print(f"📊 Performance and Error Handling Test Results: {passed} passed, {failed} failed")
        
        if failed == 0:
            print("🎉 All performance and error handling tests passed!")
        else:
            print("⚠️ Some tests failed. Please review the issues above.")
        
        return failed == 0

if __name__ == "__main__":
    tester = PerformanceErrorTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
