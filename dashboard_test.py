#!/usr/bin/env python3
"""
Dashboard and Data Visualization Testing for Psychiatric Assessment System
Tests dashboard functionality, charts, filtering, and data export features
"""

import sys
import os
import sqlite3
import pandas as pd
import json
import datetime
from typing import Dict, Any, List

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class DashboardTester:
    def __init__(self):
        self.test_results = []
        self.test_db_path = 'test_dashboard.db'
        self.setup_test_data()
        
    def log_test(self, test_name: str, passed: bool, message: str = ""):
        """Log test results"""
        status = "✅ PASS" if passed else "❌ FAIL"
        self.test_results.append({
            'test': test_name,
            'passed': passed,
            'message': message
        })
        print(f"{status}: {test_name}")
        if message:
            print(f"    {message}")
    
    def setup_test_data(self):
        """Create test database with sample data"""
        try:
            conn = sqlite3.connect(self.test_db_path)
            cursor = conn.cursor()
            
            # Create tables
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS patients (
                patient_id TEXT PRIMARY KEY,
                age INTEGER,
                gender TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS assessments (
                assessment_id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id TEXT,
                assessment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                suicide_risk_level TEXT,
                primary_diagnosis TEXT,
                phq9_score INTEGER,
                gad7_score INTEGER,
                completion_percentage REAL,
                data_json TEXT,
                FOREIGN KEY (patient_id) REFERENCES patients (patient_id)
            )
            ''')
            
            # Insert test patients
            test_patients = [
                ('PAT-001', 25, 'Female'),
                ('PAT-002', 35, 'Male'),
                ('PAT-003', 45, 'Female'),
                ('PAT-004', 55, 'Male'),
                ('PAT-005', 30, 'Non-binary')
            ]
            
            cursor.executemany(
                'INSERT OR REPLACE INTO patients (patient_id, age, gender) VALUES (?, ?, ?)',
                test_patients
            )
            
            # Insert test assessments
            test_assessments = [
                ('PAT-001', 'Low', 'Adjustment Disorder', 8, 6, 100.0),
                ('PAT-002', 'Moderate', 'Major Depressive Disorder', 15, 12, 95.0),
                ('PAT-003', 'High', 'Bipolar Disorder', 22, 18, 90.0),
                ('PAT-004', 'Low', 'Anxiety Disorder', 5, 8, 85.0),
                ('PAT-005', 'Moderate', 'PTSD', 18, 15, 100.0)
            ]
            
            for patient_id, risk, diagnosis, phq9, gad7, completion in test_assessments:
                cursor.execute('''
                INSERT INTO assessments (
                    patient_id, suicide_risk_level, primary_diagnosis,
                    phq9_score, gad7_score, completion_percentage, data_json
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    patient_id, risk, diagnosis, phq9, gad7, completion,
                    json.dumps({'test': 'data'})
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error setting up test data: {e}")
    
    def test_data_retrieval_functions(self):
        """Test data retrieval functions for dashboard"""
        try:
            from streamlit_sql_zai import get_all_patients, get_all_assessments
            
            # Temporarily replace database path for testing
            import streamlit_sql_zai
            original_create_connection = streamlit_sql_zai.create_connection
            
            def test_create_connection():
                return sqlite3.connect(self.test_db_path)
            
            streamlit_sql_zai.create_connection = test_create_connection
            
            # Test get_all_patients
            patients_df = get_all_patients()
            if not patients_df.empty and len(patients_df) == 5:
                self.log_test("Get All Patients", True, f"Retrieved {len(patients_df)} patients")
            else:
                self.log_test("Get All Patients", False, f"Expected 5 patients, got {len(patients_df)}")
                return False
            
            # Test get_all_assessments
            assessments_df = get_all_assessments()
            if not assessments_df.empty and len(assessments_df) == 5:
                self.log_test("Get All Assessments", True, f"Retrieved {len(assessments_df)} assessments")
            else:
                self.log_test("Get All Assessments", False, f"Expected 5 assessments, got {len(assessments_df)}")
                return False
            
            # Restore original function
            streamlit_sql_zai.create_connection = original_create_connection
            
            return True
            
        except Exception as e:
            self.log_test("Data Retrieval Functions", False, str(e))
            return False
    
    def test_data_filtering_logic(self):
        """Test data filtering and search logic"""
        try:
            # Create test DataFrame
            test_data = pd.DataFrame({
                'patient_id': ['PAT-001', 'PAT-002', 'PAT-003', 'PAT-004', 'PAT-005'],
                'age': [25, 35, 45, 55, 30],
                'gender': ['Female', 'Male', 'Female', 'Male', 'Non-binary'],
                'primary_diagnosis': ['Adjustment Disorder', 'Major Depressive Disorder', 
                                    'Bipolar Disorder', 'Anxiety Disorder', 'PTSD'],
                'suicide_risk_level': ['Low', 'Moderate', 'High', 'Low', 'Moderate'],
                'phq9_score': [8, 15, 22, 5, 18],
                'gad7_score': [6, 12, 18, 8, 15]
            })
            
            # Test age filtering
            age_filtered = test_data[test_data['age'] >= 30]
            if len(age_filtered) == 4:  # Should include ages 35, 45, 55, 30
                self.log_test("Age Filtering", True, f"Filtered to {len(age_filtered)} patients aged 30+")
            else:
                self.log_test("Age Filtering", False, f"Expected 4 patients, got {len(age_filtered)}")
                return False
            
            # Test gender filtering
            female_patients = test_data[test_data['gender'] == 'Female']
            if len(female_patients) == 2:
                self.log_test("Gender Filtering", True, f"Found {len(female_patients)} female patients")
            else:
                self.log_test("Gender Filtering", False, f"Expected 2 female patients, got {len(female_patients)}")
                return False
            
            # Test risk level filtering
            high_risk = test_data[test_data['suicide_risk_level'] == 'High']
            if len(high_risk) == 1:
                self.log_test("Risk Level Filtering", True, f"Found {len(high_risk)} high-risk patient")
            else:
                self.log_test("Risk Level Filtering", False, f"Expected 1 high-risk patient, got {len(high_risk)}")
                return False
            
            # Test diagnosis search
            depression_cases = test_data[test_data['primary_diagnosis'].str.contains('Depressive', case=False)]
            if len(depression_cases) == 1:
                self.log_test("Diagnosis Search", True, f"Found {len(depression_cases)} depression case")
            else:
                self.log_test("Diagnosis Search", False, f"Expected 1 depression case, got {len(depression_cases)}")
                return False
            
            # Test score range filtering
            moderate_depression = test_data[(test_data['phq9_score'] >= 10) & (test_data['phq9_score'] <= 19)]
            if len(moderate_depression) == 2:  # Scores 15 and 18
                self.log_test("Score Range Filtering", True, f"Found {len(moderate_depression)} moderate depression cases")
            else:
                self.log_test("Score Range Filtering", False, f"Expected 2 moderate cases, got {len(moderate_depression)}")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("Data Filtering Logic", False, str(e))
            return False
    
    def test_statistical_calculations(self):
        """Test statistical calculations for dashboard metrics"""
        try:
            # Create test data
            test_scores = pd.DataFrame({
                'phq9_score': [8, 15, 22, 5, 18],
                'gad7_score': [6, 12, 18, 8, 15],
                'age': [25, 35, 45, 55, 30],
                'suicide_risk_level': ['Low', 'Moderate', 'High', 'Low', 'Moderate']
            })
            
            # Test mean calculations
            mean_phq9 = test_scores['phq9_score'].mean()
            expected_mean_phq9 = (8 + 15 + 22 + 5 + 18) / 5  # 13.6
            if abs(mean_phq9 - expected_mean_phq9) < 0.1:
                self.log_test("PHQ-9 Mean Calculation", True, f"Mean PHQ-9: {mean_phq9:.1f}")
            else:
                self.log_test("PHQ-9 Mean Calculation", False, f"Expected {expected_mean_phq9}, got {mean_phq9}")
                return False
            
            # Test median calculations
            median_gad7 = test_scores['gad7_score'].median()
            expected_median_gad7 = 12.0  # Middle value of [6, 8, 12, 15, 18]
            if median_gad7 == expected_median_gad7:
                self.log_test("GAD-7 Median Calculation", True, f"Median GAD-7: {median_gad7}")
            else:
                self.log_test("GAD-7 Median Calculation", False, f"Expected {expected_median_gad7}, got {median_gad7}")
                return False
            
            # Test risk level distribution
            risk_counts = test_scores['suicide_risk_level'].value_counts()
            if risk_counts['Low'] == 2 and risk_counts['Moderate'] == 2 and risk_counts['High'] == 1:
                self.log_test("Risk Level Distribution", True, f"Low: {risk_counts['Low']}, Moderate: {risk_counts['Moderate']}, High: {risk_counts['High']}")
            else:
                self.log_test("Risk Level Distribution", False, f"Unexpected distribution: {risk_counts.to_dict()}")
                return False
            
            # Test age group analysis
            age_groups = pd.cut(test_scores['age'], bins=[0, 30, 50, 100], labels=['Young', 'Middle', 'Older'])
            age_group_counts = age_groups.value_counts()
            if age_group_counts['Young'] == 2 and age_group_counts['Middle'] == 3:
                self.log_test("Age Group Analysis", True, f"Young: {age_group_counts['Young']}, Middle: {age_group_counts['Middle']}")
            else:
                self.log_test("Age Group Analysis", False, f"Unexpected age groups: {age_group_counts.to_dict()}")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("Statistical Calculations", False, str(e))
            return False
    
    def test_chart_data_preparation(self):
        """Test data preparation for charts and visualizations"""
        try:
            # Test data for bar chart (risk level distribution)
            risk_data = pd.Series(['Low', 'Moderate', 'High', 'Low', 'Moderate'])
            risk_counts = risk_data.value_counts()
            
            # Prepare data for plotting
            chart_data = pd.DataFrame({
                'Risk Level': risk_counts.index,
                'Count': risk_counts.values
            })
            
            if len(chart_data) == 3 and chart_data['Count'].sum() == 5:
                self.log_test("Bar Chart Data Preparation", True, f"Prepared data for {len(chart_data)} risk levels")
            else:
                self.log_test("Bar Chart Data Preparation", False, f"Unexpected chart data: {len(chart_data)} categories")
                return False
            
            # Test data for histogram (score distribution)
            scores = pd.Series([8, 15, 22, 5, 18])
            bins = [0, 5, 10, 15, 20, 25]
            score_hist, bin_edges = pd.cut(scores, bins=bins, retbins=True)
            hist_counts = score_hist.value_counts().sort_index()
            
            if len(hist_counts) <= len(bins) - 1:
                self.log_test("Histogram Data Preparation", True, f"Prepared histogram with {len(hist_counts)} bins")
            else:
                self.log_test("Histogram Data Preparation", False, f"Unexpected histogram bins: {len(hist_counts)}")
                return False
            
            # Test data for time series (assessments over time)
            dates = pd.date_range(start='2024-01-01', periods=5, freq='D')
            time_series_data = pd.DataFrame({
                'date': dates,
                'assessments': [1, 2, 1, 3, 2]
            })
            
            if len(time_series_data) == 5 and time_series_data['assessments'].sum() == 9:
                self.log_test("Time Series Data Preparation", True, f"Prepared {len(time_series_data)} time points")
            else:
                self.log_test("Time Series Data Preparation", False, f"Unexpected time series data")
                return False
            
            # Test data for scatter plot (PHQ-9 vs GAD-7)
            scatter_data = pd.DataFrame({
                'phq9': [8, 15, 22, 5, 18],
                'gad7': [6, 12, 18, 8, 15]
            })
            
            # Check correlation
            correlation = scatter_data['phq9'].corr(scatter_data['gad7'])
            if -1 <= correlation <= 1:
                self.log_test("Scatter Plot Data Preparation", True, f"PHQ-9 vs GAD-7 correlation: {correlation:.3f}")
            else:
                self.log_test("Scatter Plot Data Preparation", False, f"Invalid correlation: {correlation}")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("Chart Data Preparation", False, str(e))
            return False
    
    def test_data_export_functionality(self):
        """Test data export functionality"""
        try:
            # Create test DataFrame
            export_data = pd.DataFrame({
                'patient_id': ['PAT-001', 'PAT-002', 'PAT-003'],
                'age': [25, 35, 45],
                'gender': ['Female', 'Male', 'Female'],
                'phq9_score': [8, 15, 22],
                'gad7_score': [6, 12, 18],
                'primary_diagnosis': ['Adjustment Disorder', 'Major Depressive Disorder', 'Bipolar Disorder']
            })
            
            # Test CSV export
            csv_filename = 'test_export.csv'
            export_data.to_csv(csv_filename, index=False)
            
            # Verify CSV file was created and can be read back
            if os.path.exists(csv_filename):
                imported_data = pd.read_csv(csv_filename)
                if len(imported_data) == 3 and list(imported_data.columns) == list(export_data.columns):
                    self.log_test("CSV Export", True, f"Exported and verified {len(imported_data)} records")
                    os.remove(csv_filename)  # Clean up
                else:
                    self.log_test("CSV Export", False, f"CSV verification failed")
                    return False
            else:
                self.log_test("CSV Export", False, "CSV file not created")
                return False
            
            # Test JSON export
            json_filename = 'test_export.json'
            export_data.to_json(json_filename, orient='records', indent=2)
            
            # Verify JSON file was created and can be read back
            if os.path.exists(json_filename):
                with open(json_filename, 'r') as f:
                    imported_json = json.load(f)
                if len(imported_json) == 3 and 'patient_id' in imported_json[0]:
                    self.log_test("JSON Export", True, f"Exported and verified {len(imported_json)} records")
                    os.remove(json_filename)  # Clean up
                else:
                    self.log_test("JSON Export", False, "JSON verification failed")
                    return False
            else:
                self.log_test("JSON Export", False, "JSON file not created")
                return False
            
            # Test ML dataset preparation
            ml_features = export_data[['age', 'phq9_score', 'gad7_score']].copy()
            ml_features['gender_encoded'] = export_data['gender'].map({'Female': 0, 'Male': 1}).fillna(2)
            
            if len(ml_features.columns) == 4 and ml_features.isnull().sum().sum() == 0:
                self.log_test("ML Dataset Preparation", True, f"Prepared {len(ml_features.columns)} features")
            else:
                self.log_test("ML Dataset Preparation", False, "ML dataset preparation failed")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("Data Export Functionality", False, str(e))
            return False
    
    def test_dashboard_metrics(self):
        """Test dashboard key metrics calculations"""
        try:
            # Create test data
            test_data = pd.DataFrame({
                'assessment_date': pd.date_range(start='2024-01-01', periods=10, freq='D'),
                'phq9_score': [5, 8, 12, 15, 18, 22, 7, 10, 14, 20],
                'gad7_score': [3, 6, 9, 12, 15, 18, 5, 8, 11, 16],
                'suicide_risk_level': ['Low', 'Low', 'Moderate', 'Moderate', 'High', 'High', 'Low', 'Moderate', 'Moderate', 'High'],
                'completion_percentage': [100, 95, 90, 85, 100, 95, 90, 100, 95, 90]
            })
            
            # Test total assessments
            total_assessments = len(test_data)
            if total_assessments == 10:
                self.log_test("Total Assessments Metric", True, f"Total: {total_assessments}")
            else:
                self.log_test("Total Assessments Metric", False, f"Expected 10, got {total_assessments}")
                return False
            
            # Test average completion rate
            avg_completion = test_data['completion_percentage'].mean()
            expected_completion = 94.0  # Average of the completion percentages
            if abs(avg_completion - expected_completion) < 0.1:
                self.log_test("Average Completion Rate", True, f"Average: {avg_completion:.1f}%")
            else:
                self.log_test("Average Completion Rate", False, f"Expected {expected_completion}, got {avg_completion}")
                return False
            
            # Test high-risk patient count
            high_risk_count = len(test_data[test_data['suicide_risk_level'] == 'High'])
            if high_risk_count == 3:
                self.log_test("High-Risk Patient Count", True, f"High-risk patients: {high_risk_count}")
            else:
                self.log_test("High-Risk Patient Count", False, f"Expected 3, got {high_risk_count}")
                return False
            
            # Test average PHQ-9 score
            avg_phq9 = test_data['phq9_score'].mean()
            expected_avg_phq9 = 13.1  # Average of the PHQ-9 scores
            if abs(avg_phq9 - expected_avg_phq9) < 0.1:
                self.log_test("Average PHQ-9 Score", True, f"Average: {avg_phq9:.1f}")
            else:
                self.log_test("Average PHQ-9 Score", False, f"Expected {expected_avg_phq9}, got {avg_phq9}")
                return False
            
            # Test assessments per day
            daily_counts = test_data.groupby(test_data['assessment_date'].dt.date).size()
            if len(daily_counts) == 10 and daily_counts.max() == 1:  # One assessment per day
                self.log_test("Daily Assessment Counts", True, f"Assessments across {len(daily_counts)} days")
            else:
                self.log_test("Daily Assessment Counts", False, f"Unexpected daily distribution")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("Dashboard Metrics", False, str(e))
            return False
    
    def cleanup_test_data(self):
        """Clean up test database"""
        try:
            if os.path.exists(self.test_db_path):
                os.remove(self.test_db_path)
            self.log_test("Test Data Cleanup", True, "Test database removed")
        except Exception as e:
            self.log_test("Test Data Cleanup", False, str(e))
    
    def run_all_tests(self):
        """Run all dashboard tests"""
        print("🧪 Starting Dashboard and Data Visualization Testing")
        print("=" * 60)
        
        tests = [
            ("Data Retrieval Functions", self.test_data_retrieval_functions),
            ("Data Filtering Logic", self.test_data_filtering_logic),
            ("Statistical Calculations", self.test_statistical_calculations),
            ("Chart Data Preparation", self.test_chart_data_preparation),
            ("Data Export Functionality", self.test_data_export_functionality),
            ("Dashboard Metrics", self.test_dashboard_metrics),
            ("Cleanup", self.cleanup_test_data)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                self.log_test(test_name, False, f"Exception: {e}")
                failed += 1
        
        print("\n" + "=" * 60)
        print(f"📊 Dashboard Test Results: {passed} passed, {failed} failed")
        
        if failed == 0:
            print("🎉 All dashboard tests passed!")
        else:
            print("⚠️ Some dashboard tests failed. Please review the issues above.")
        
        return failed == 0

if __name__ == "__main__":
    tester = DashboardTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
