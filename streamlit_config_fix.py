#!/usr/bin/env python3
"""
Configuration fixes for Streamlit port binding issues
This script helps resolve common Streamlit startup problems
"""

import os
import sys
import socket
import subprocess
import time
from pathlib import Path

def find_available_port(start_port=8501, max_attempts=10):
    """Find an available port starting from start_port"""
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    return None

def create_streamlit_config():
    """Create Streamlit configuration file to fix common issues"""
    config_dir = Path.home() / '.streamlit'
    config_dir.mkdir(exist_ok=True)
    
    config_file = config_dir / 'config.toml'
    
    # Find available port
    available_port = find_available_port()
    if not available_port:
        available_port = 8502  # fallback
    
    config_content = f"""
[server]
port = {available_port}
address = "localhost"
headless = false
runOnSave = true
allowRunOnSave = true
enableCORS = false
enableXsrfProtection = false
maxUploadSize = 200

[browser]
gatherUsageStats = false
serverAddress = "localhost"
serverPort = {available_port}

[theme]
primaryColor = "#3b82f6"
backgroundColor = "#ffffff"
secondaryBackgroundColor = "#f1f5f9"
textColor = "#1e293b"

[logger]
level = "info"
messageFormat = "%(asctime)s %(message)s"
"""
    
    with open(config_file, 'w') as f:
        f.write(config_content)
    
    print(f"✅ Streamlit config created at {config_file}")
    print(f"✅ Using port {available_port}")
    return available_port

def kill_existing_streamlit_processes():
    """Kill any existing Streamlit processes that might be blocking ports"""
    try:
        if os.name == 'nt':  # Windows
            subprocess.run(['taskkill', '/f', '/im', 'streamlit.exe'], 
                         capture_output=True, check=False)
        else:  # Unix-like
            subprocess.run(['pkill', '-f', 'streamlit'], 
                         capture_output=True, check=False)
        print("✅ Cleared existing Streamlit processes")
    except Exception as e:
        print(f"⚠️ Could not clear existing processes: {e}")

def run_streamlit_with_config(script_path="streamlit_sql_zai.py"):
    """Run Streamlit with proper configuration"""
    
    # Kill existing processes
    kill_existing_streamlit_processes()
    time.sleep(2)
    
    # Create config
    port = create_streamlit_config()
    
    # Check if script exists
    if not os.path.exists(script_path):
        print(f"❌ Script {script_path} not found")
        return False
    
    # Run Streamlit
    try:
        print(f"🚀 Starting Streamlit on port {port}...")
        print(f"📂 Running script: {script_path}")
        
        cmd = [
            sys.executable, "-m", "streamlit", "run", script_path,
            "--server.port", str(port),
            "--server.address", "localhost",
            "--server.headless", "false",
            "--browser.gatherUsageStats", "false"
        ]
        
        print(f"🔧 Command: {' '.join(cmd)}")
        
        # Start the process
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # Monitor the process
        print("📊 Monitoring Streamlit startup...")
        startup_timeout = 30  # 30 seconds timeout
        start_time = time.time()
        
        while process.poll() is None:
            if time.time() - start_time > startup_timeout:
                print("⏰ Startup timeout reached")
                process.terminate()
                return False
            
            # Check if port is being used (indicates successful startup)
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    result = s.connect_ex(('localhost', port))
                    if result == 0:
                        print(f"✅ Streamlit is running on http://localhost:{port}")
                        print("🌐 You can now access the application in your browser")
                        return True
            except:
                pass
            
            time.sleep(1)
        
        # Process ended, check for errors
        stdout, stderr = process.communicate()
        if process.returncode != 0:
            print(f"❌ Streamlit failed to start:")
            print(f"Return code: {process.returncode}")
            if stderr:
                print(f"Error output: {stderr}")
            if stdout:
                print(f"Standard output: {stdout}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error starting Streamlit: {e}")
        return False

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = [
        'streamlit',
        'pandas',
        'plotly',
        'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} is missing")
    
    if missing_packages:
        print(f"\n📦 Install missing packages with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def diagnose_issues():
    """Diagnose common Streamlit issues"""
    print("🔍 Diagnosing Streamlit issues...")
    
    # Check Python version
    python_version = sys.version_info
    print(f"🐍 Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 7):
        print("⚠️ Python 3.7+ is recommended for Streamlit")
    
    # Check dependencies
    if not check_dependencies():
        return False
    
    # Check available ports
    available_ports = []
    for port in range(8501, 8511):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                available_ports.append(port)
        except OSError:
            pass
    
    if available_ports:
        print(f"✅ Available ports: {available_ports}")
    else:
        print("❌ No available ports in range 8501-8510")
        return False
    
    # Check file permissions
    try:
        test_file = Path.home() / '.streamlit' / 'test_write'
        test_file.parent.mkdir(exist_ok=True)
        test_file.write_text("test")
        test_file.unlink()
        print("✅ File permissions OK")
    except Exception as e:
        print(f"❌ File permission issue: {e}")
        return False
    
    return True

def main():
    """Main function to fix and run Streamlit"""
    print("🔧 Streamlit Configuration Fix Tool")
    print("=" * 50)
    
    # Diagnose issues
    if not diagnose_issues():
        print("\n❌ Please fix the issues above before continuing")
        return False
    
    print("\n🚀 Attempting to start Streamlit...")
    
    # Try to run Streamlit
    success = run_streamlit_with_config()
    
    if success:
        print("\n🎉 Streamlit started successfully!")
        print("💡 If the browser doesn't open automatically, check the URL above")
    else:
        print("\n❌ Failed to start Streamlit")
        print("💡 Try running manually with: streamlit run streamlit_sql_zai.py --server.port 8502")
    
    return success

if __name__ == "__main__":
    main()
