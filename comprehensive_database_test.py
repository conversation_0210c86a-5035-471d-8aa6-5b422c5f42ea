#!/usr/bin/env python3
"""
Comprehensive Database Testing for Psychiatric Assessment System
Tests all database functionality without requiring Streamlit UI
"""

import sqlite3
import json
import datetime
import uuid
import os
import sys
from typing import Dict, Any, List, Tuple
import traceback

class DatabaseTester:
    def __init__(self, db_path='test_psychiatric_assessments.db'):
        self.db_path = db_path
        self.test_results = []
        
    def log_test(self, test_name: str, passed: bool, message: str = ""):
        """Log test results"""
        status = "✅ PASS" if passed else "❌ FAIL"
        self.test_results.append({
            'test': test_name,
            'passed': passed,
            'message': message
        })
        print(f"{status}: {test_name}")
        if message:
            print(f"    {message}")
    
    def create_connection(self) -> sqlite3.Connection:
        """Create database connection with proper settings"""
        try:
            conn = sqlite3.connect(
                self.db_path,
                timeout=30.0,
                check_same_thread=False
            )
            conn.execute("PRAGMA foreign_keys = ON")
            conn.execute("PRAGMA journal_mode = WAL")
            return conn
        except Exception as e:
            raise Exception(f"Database connection failed: {e}")
    
    def test_database_creation(self):
        """Test database and table creation"""
        try:
            conn = self.create_connection()
            cursor = conn.cursor()
            
            # Create enhanced patients table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS patients (
                patient_id TEXT PRIMARY KEY,
                age INTEGER CHECK (age >= 0 AND age <= 150),
                gender TEXT,
                sex_assigned TEXT,
                marital_status TEXT,
                children TEXT,
                education TEXT,
                occupation TEXT,
                employment_status TEXT,
                ethnicity TEXT,
                living_situation TEXT,
                housing_stability TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            # Create enhanced assessments table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS assessments (
                assessment_id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id TEXT NOT NULL,
                assessment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                chief_complaint TEXT,
                history_present_illness TEXT,
                past_psychiatric_history TEXT,
                past_medical_history TEXT,
                family_history TEXT,
                social_developmental_history TEXT,
                substance_use TEXT,
                mental_state_examination TEXT,
                cognitive_assessment TEXT,
                risk_assessment TEXT,
                laboratory_investigations TEXT,
                clinical_scales TEXT,
                diagnostic_formulation TEXT,
                treatment_planning TEXT,
                follow_up_monitoring TEXT,
                suicide_risk_level TEXT CHECK (suicide_risk_level IN ('Low', 'Moderate', 'High', 'Imminent')),
                primary_diagnosis TEXT,
                phq9_score INTEGER CHECK (phq9_score >= 0 AND phq9_score <= 27),
                gad7_score INTEGER CHECK (gad7_score >= 0 AND gad7_score <= 21),
                duration_minutes REAL CHECK (duration_minutes >= 0),
                completion_percentage REAL CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
                data_json TEXT,
                created_by TEXT DEFAULT 'system',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (patient_id) REFERENCES patients (patient_id) ON DELETE CASCADE
            )
            ''')
            
            # Create indexes
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_patient_id ON assessments (patient_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_assessment_date ON assessments (assessment_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_primary_diagnosis ON assessments (primary_diagnosis)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_suicide_risk ON assessments (suicide_risk_level)')
            
            conn.commit()
            conn.close()
            
            self.log_test("Database Creation", True, "Tables and indexes created successfully")
            return True
            
        except Exception as e:
            self.log_test("Database Creation", False, str(e))
            return False
    
    def test_patient_operations(self):
        """Test patient CRUD operations"""
        try:
            conn = self.create_connection()
            cursor = conn.cursor()
            
            # Test patient insertion
            test_patient = {
                'patient_id': 'TEST-001',
                'age': 35,
                'gender': 'Male',
                'sex_assigned': 'Male',
                'marital_status': 'Married',
                'children': '2',
                'education': "Bachelor's degree",
                'occupation': 'Engineer',
                'employment_status': 'Employed full-time',
                'ethnicity': 'White/Caucasian',
                'living_situation': 'With spouse/partner',
                'housing_stability': 'Stable housing'
            }
            
            cursor.execute('''
            INSERT OR REPLACE INTO patients (
                patient_id, age, gender, sex_assigned, marital_status, children,
                education, occupation, employment_status, ethnicity, living_situation,
                housing_stability
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                test_patient['patient_id'],
                test_patient['age'],
                test_patient['gender'],
                test_patient['sex_assigned'],
                test_patient['marital_status'],
                test_patient['children'],
                test_patient['education'],
                test_patient['occupation'],
                test_patient['employment_status'],
                test_patient['ethnicity'],
                test_patient['living_situation'],
                test_patient['housing_stability']
            ))
            
            # Test patient retrieval
            cursor.execute("SELECT * FROM patients WHERE patient_id = ?", (test_patient['patient_id'],))
            retrieved_patient = cursor.fetchone()
            
            if retrieved_patient:
                self.log_test("Patient Insert/Retrieve", True, f"Patient {test_patient['patient_id']} saved and retrieved")
            else:
                self.log_test("Patient Insert/Retrieve", False, "Failed to retrieve inserted patient")
                return False
            
            # Test patient update
            cursor.execute("UPDATE patients SET age = ? WHERE patient_id = ?", (36, test_patient['patient_id']))
            cursor.execute("SELECT age FROM patients WHERE patient_id = ?", (test_patient['patient_id'],))
            updated_age = cursor.fetchone()[0]
            
            if updated_age == 36:
                self.log_test("Patient Update", True, "Patient age updated successfully")
            else:
                self.log_test("Patient Update", False, f"Expected age 36, got {updated_age}")
                return False
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            self.log_test("Patient Operations", False, str(e))
            return False
    
    def test_assessment_operations(self):
        """Test assessment CRUD operations"""
        try:
            conn = self.create_connection()
            cursor = conn.cursor()
            
            # Create test assessment data
            test_assessment = {
                'patient_id': 'TEST-001',
                'chief_complaint': json.dumps({'complaint': 'Depression and anxiety'}),
                'suicide_risk_level': 'Moderate',
                'primary_diagnosis': 'Major Depressive Disorder',
                'phq9_score': 15,
                'gad7_score': 12,
                'duration_minutes': 45.5,
                'completion_percentage': 85.0,
                'data_json': json.dumps({
                    'demographics': {'age': 35, 'gender': 'Male'},
                    'clinical_scales': {
                        'depression': {'phq9_total': 15},
                        'anxiety': {'gad7_total': 12}
                    }
                })
            }
            
            # Insert assessment
            cursor.execute('''
            INSERT INTO assessments (
                patient_id, chief_complaint, suicide_risk_level, primary_diagnosis,
                phq9_score, gad7_score, duration_minutes, completion_percentage, data_json
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                test_assessment['patient_id'],
                test_assessment['chief_complaint'],
                test_assessment['suicide_risk_level'],
                test_assessment['primary_diagnosis'],
                test_assessment['phq9_score'],
                test_assessment['gad7_score'],
                test_assessment['duration_minutes'],
                test_assessment['completion_percentage'],
                test_assessment['data_json']
            ))
            
            assessment_id = cursor.lastrowid
            
            # Retrieve assessment
            cursor.execute("SELECT * FROM assessments WHERE assessment_id = ?", (assessment_id,))
            retrieved_assessment = cursor.fetchone()
            
            if retrieved_assessment:
                self.log_test("Assessment Insert/Retrieve", True, f"Assessment {assessment_id} saved and retrieved")
            else:
                self.log_test("Assessment Insert/Retrieve", False, "Failed to retrieve inserted assessment")
                return False
            
            # Test JSON data parsing
            try:
                json_data = json.loads(retrieved_assessment[23])  # data_json column
                if 'demographics' in json_data and 'clinical_scales' in json_data:
                    self.log_test("JSON Data Parsing", True, "Assessment JSON data parsed successfully")
                else:
                    self.log_test("JSON Data Parsing", False, "JSON data missing expected fields")
                    return False
            except Exception as e:
                self.log_test("JSON Data Parsing", False, f"JSON parsing failed: {e}")
                return False
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            self.log_test("Assessment Operations", False, str(e))
            return False
    
    def test_clinical_scales_validation(self):
        """Test clinical scales calculations and validation"""
        try:
            # Test PHQ-9 calculation
            phq9_scores = {f'q{i}': 2 for i in range(1, 10)}  # All questions scored as 2
            expected_total = 18
            actual_total = sum(phq9_scores.values())
            
            if actual_total == expected_total:
                self.log_test("PHQ-9 Calculation", True, f"PHQ-9 total: {actual_total}/27")
            else:
                self.log_test("PHQ-9 Calculation", False, f"Expected {expected_total}, got {actual_total}")
                return False
            
            # Test GAD-7 calculation
            gad7_scores = {f'q{i}': 1 for i in range(1, 8)}  # All questions scored as 1
            expected_total = 7
            actual_total = sum(gad7_scores.values())
            
            if actual_total == expected_total:
                self.log_test("GAD-7 Calculation", True, f"GAD-7 total: {actual_total}/21")
            else:
                self.log_test("GAD-7 Calculation", False, f"Expected {expected_total}, got {actual_total}")
                return False
            
            # Test score validation ranges
            valid_phq9_scores = [0, 5, 15, 27]
            invalid_phq9_scores = [-1, 28, 50]
            
            for score in valid_phq9_scores:
                if 0 <= score <= 27:
                    continue
                else:
                    self.log_test("PHQ-9 Validation", False, f"Valid score {score} failed validation")
                    return False
            
            self.log_test("PHQ-9 Validation", True, "All valid PHQ-9 scores passed validation")
            
            for score in invalid_phq9_scores:
                if not (0 <= score <= 27):
                    continue
                else:
                    self.log_test("PHQ-9 Validation", False, f"Invalid score {score} passed validation")
                    return False
            
            self.log_test("PHQ-9 Invalid Score Detection", True, "All invalid PHQ-9 scores correctly rejected")
            
            return True
            
        except Exception as e:
            self.log_test("Clinical Scales Validation", False, str(e))
            return False
    
    def test_risk_assessment_logic(self):
        """Test suicide risk assessment calculations"""
        try:
            # Test low risk scenario
            low_risk_data = {
                'suicide': {
                    'current_si': 'None',
                    'previous_attempts': 'None',
                    'means_access': 'No access',
                    'family_suicide': 'No'
                }
            }
            
            # Simple risk calculation logic
            risk_score = 0
            si_scores = {
                'None': 0,
                'Passive (wish to be dead)': 1,
                'Active ideation without plan': 2,
                'Active ideation with plan': 3,
                'Active ideation with intent': 4
            }
            
            risk_score += si_scores.get(low_risk_data['suicide']['current_si'], 0) * 3
            
            if low_risk_data['suicide']['previous_attempts'] != 'None':
                risk_score += 2
            
            if low_risk_data['suicide']['means_access'] == 'Immediate access':
                risk_score += 2
            
            if low_risk_data['suicide']['family_suicide'] in ['Yes - completed', 'Yes - attempted']:
                risk_score += 1
            
            # Determine risk level
            if risk_score >= 12:
                risk_level = 'Imminent'
            elif risk_score >= 8:
                risk_level = 'High'
            elif risk_score >= 4:
                risk_level = 'Moderate'
            else:
                risk_level = 'Low'
            
            if risk_level == 'Low':
                self.log_test("Low Risk Assessment", True, f"Risk level: {risk_level} (score: {risk_score})")
            else:
                self.log_test("Low Risk Assessment", False, f"Expected Low, got {risk_level}")
                return False
            
            # Test high risk scenario
            high_risk_data = {
                'suicide': {
                    'current_si': 'Active ideation with plan',
                    'previous_attempts': '1 attempt',
                    'means_access': 'Immediate access',
                    'family_suicide': 'Yes - completed'
                }
            }
            
            risk_score = 0
            risk_score += si_scores.get(high_risk_data['suicide']['current_si'], 0) * 3  # 3 * 3 = 9
            
            if high_risk_data['suicide']['previous_attempts'] != 'None':
                risk_score += 2  # 9 + 2 = 11
            
            if high_risk_data['suicide']['means_access'] == 'Immediate access':
                risk_score += 2  # 11 + 2 = 13
            
            if high_risk_data['suicide']['family_suicide'] in ['Yes - completed', 'Yes - attempted']:
                risk_score += 1  # 13 + 1 = 14
            
            # Determine risk level
            if risk_score >= 12:
                risk_level = 'Imminent'
            elif risk_score >= 8:
                risk_level = 'High'
            elif risk_score >= 4:
                risk_level = 'Moderate'
            else:
                risk_level = 'Low'
            
            if risk_level in ['High', 'Imminent']:
                self.log_test("High Risk Assessment", True, f"Risk level: {risk_level} (score: {risk_score})")
            else:
                self.log_test("High Risk Assessment", False, f"Expected High/Imminent, got {risk_level}")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("Risk Assessment Logic", False, str(e))
            return False
    
    def test_data_integrity(self):
        """Test data integrity constraints"""
        try:
            conn = self.create_connection()
            cursor = conn.cursor()
            
            # Test foreign key constraint
            try:
                cursor.execute('''
                INSERT INTO assessments (patient_id, suicide_risk_level)
                VALUES ('NONEXISTENT-PATIENT', 'Low')
                ''')
                conn.commit()
                self.log_test("Foreign Key Constraint", False, "Foreign key constraint not enforced")
                return False
            except sqlite3.IntegrityError:
                self.log_test("Foreign Key Constraint", True, "Foreign key constraint properly enforced")
            
            # Test check constraints
            try:
                cursor.execute('''
                INSERT INTO patients (patient_id, age) VALUES ('TEST-INVALID', 200)
                ''')
                conn.commit()
                self.log_test("Age Check Constraint", False, "Age constraint not enforced")
                return False
            except sqlite3.IntegrityError:
                self.log_test("Age Check Constraint", True, "Age constraint properly enforced")
            
            # Test PHQ-9 score constraint
            try:
                cursor.execute('''
                INSERT INTO assessments (patient_id, phq9_score)
                VALUES ('TEST-001', 50)
                ''')
                conn.commit()
                self.log_test("PHQ-9 Score Constraint", False, "PHQ-9 score constraint not enforced")
                return False
            except sqlite3.IntegrityError:
                self.log_test("PHQ-9 Score Constraint", True, "PHQ-9 score constraint properly enforced")
            
            conn.close()
            return True
            
        except Exception as e:
            self.log_test("Data Integrity", False, str(e))
            return False
    
    def test_performance(self):
        """Test database performance with multiple records"""
        try:
            conn = self.create_connection()
            cursor = conn.cursor()
            
            # Insert multiple test patients
            start_time = datetime.datetime.now()
            
            for i in range(100):
                patient_id = f"PERF-TEST-{i:03d}"
                cursor.execute('''
                INSERT OR REPLACE INTO patients (patient_id, age, gender)
                VALUES (?, ?, ?)
                ''', (patient_id, 25 + (i % 50), 'Male' if i % 2 == 0 else 'Female'))
            
            conn.commit()
            insert_time = (datetime.datetime.now() - start_time).total_seconds()
            
            # Query performance test
            start_time = datetime.datetime.now()
            cursor.execute("SELECT COUNT(*) FROM patients WHERE age > 30")
            count = cursor.fetchone()[0]
            query_time = (datetime.datetime.now() - start_time).total_seconds()
            
            conn.close()
            
            if insert_time < 1.0:  # Should complete in under 1 second
                self.log_test("Insert Performance", True, f"100 inserts in {insert_time:.3f}s")
            else:
                self.log_test("Insert Performance", False, f"100 inserts took {insert_time:.3f}s (too slow)")
                return False
            
            if query_time < 0.1:  # Should complete in under 0.1 seconds
                self.log_test("Query Performance", True, f"Query completed in {query_time:.3f}s (found {count} records)")
            else:
                self.log_test("Query Performance", False, f"Query took {query_time:.3f}s (too slow)")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("Performance Testing", False, str(e))
            return False
    
    def cleanup_test_data(self):
        """Clean up test data"""
        try:
            conn = self.create_connection()
            cursor = conn.cursor()
            
            # Delete test data
            cursor.execute("DELETE FROM assessments WHERE patient_id LIKE 'TEST-%' OR patient_id LIKE 'PERF-TEST-%'")
            cursor.execute("DELETE FROM patients WHERE patient_id LIKE 'TEST-%' OR patient_id LIKE 'PERF-TEST-%'")
            
            conn.commit()
            conn.close()
            
            # Remove test database file
            if os.path.exists(self.db_path):
                os.remove(self.db_path)
            
            self.log_test("Cleanup", True, "Test data cleaned up successfully")
            return True
            
        except Exception as e:
            self.log_test("Cleanup", False, str(e))
            return False
    
    def run_all_tests(self):
        """Run all database tests"""
        print("🧪 Starting Comprehensive Database Testing")
        print("=" * 60)
        
        tests = [
            ("Database Creation", self.test_database_creation),
            ("Patient Operations", self.test_patient_operations),
            ("Assessment Operations", self.test_assessment_operations),
            ("Clinical Scales Validation", self.test_clinical_scales_validation),
            ("Risk Assessment Logic", self.test_risk_assessment_logic),
            ("Data Integrity", self.test_data_integrity),
            ("Performance Testing", self.test_performance),
            ("Cleanup", self.cleanup_test_data)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                self.log_test(test_name, False, f"Exception: {e}")
                failed += 1
        
        print("\n" + "=" * 60)
        print(f"📊 Database Test Results: {passed} passed, {failed} failed")
        
        if failed == 0:
            print("🎉 All database tests passed! Database functionality is working correctly.")
        else:
            print("⚠️ Some database tests failed. Please review the issues above.")
        
        return failed == 0

if __name__ == "__main__":
    tester = DatabaseTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
