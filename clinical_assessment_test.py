#!/usr/bin/env python3
"""
Clinical Assessment Testing for Psychiatric Assessment System
Comprehensive testing of PHQ-9, GAD-7, and risk assessment algorithms
"""

import sys
import os
import datetime
import json
from typing import Dict, Any, List, Tuple

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class ClinicalAssessmentTester:
    def __init__(self):
        self.test_results = []
        
    def log_test(self, test_name: str, passed: bool, message: str = ""):
        """Log test results"""
        status = "✅ PASS" if passed else "❌ FAIL"
        self.test_results.append({
            'test': test_name,
            'passed': passed,
            'message': message
        })
        print(f"{status}: {test_name}")
        if message:
            print(f"    {message}")
    
    def test_phq9_scoring_scenarios(self):
        """Test PHQ-9 scoring with various clinical scenarios"""
        try:
            # Test minimal depression (all 0s)
            minimal_scores = {f'q{i}': 0 for i in range(1, 10)}
            total = sum(minimal_scores.values())
            if total == 0:
                self.log_test("PHQ-9 Minimal Depression", True, f"Score: {total}/27 (Minimal)")
            else:
                self.log_test("PHQ-9 Minimal Depression", False, f"Expected 0, got {total}")
                return False
            
            # Test mild depression (scores of 1)
            mild_scores = {f'q{i}': 1 for i in range(1, 10)}
            total = sum(mild_scores.values())
            if total == 9:
                self.log_test("PHQ-9 Mild Depression", True, f"Score: {total}/27 (Mild)")
            else:
                self.log_test("PHQ-9 Mild Depression", False, f"Expected 9, got {total}")
                return False
            
            # Test moderate depression (scores of 2)
            moderate_scores = {f'q{i}': 2 for i in range(1, 10)}
            total = sum(moderate_scores.values())
            if total == 18:
                self.log_test("PHQ-9 Moderate Depression", True, f"Score: {total}/27 (Moderate)")
            else:
                self.log_test("PHQ-9 Moderate Depression", False, f"Expected 18, got {total}")
                return False
            
            # Test severe depression (all 3s)
            severe_scores = {f'q{i}': 3 for i in range(1, 10)}
            total = sum(severe_scores.values())
            if total == 27:
                self.log_test("PHQ-9 Severe Depression", True, f"Score: {total}/27 (Severe)")
            else:
                self.log_test("PHQ-9 Severe Depression", False, f"Expected 27, got {total}")
                return False
            
            # Test mixed scores (realistic scenario)
            mixed_scores = {'q1': 2, 'q2': 3, 'q3': 1, 'q4': 2, 'q5': 1, 'q6': 2, 'q7': 1, 'q8': 0, 'q9': 1}
            total = sum(mixed_scores.values())
            expected = 13
            if total == expected:
                self.log_test("PHQ-9 Mixed Scores", True, f"Score: {total}/27 (Moderate)")
            else:
                self.log_test("PHQ-9 Mixed Scores", False, f"Expected {expected}, got {total}")
                return False
            
            # Test clinical interpretation ranges
            interpretations = [
                (0, 4, "Minimal"),
                (5, 9, "Mild"),
                (10, 14, "Moderate"),
                (15, 19, "Moderately Severe"),
                (20, 27, "Severe")
            ]
            
            for min_score, max_score, severity in interpretations:
                # Test boundary values
                if min_score == 0:
                    test_score = 0
                else:
                    test_score = min_score
                
                if min_score <= test_score <= max_score:
                    self.log_test(f"PHQ-9 {severity} Range (Low)", True, f"Score {test_score} in range {min_score}-{max_score}")
                else:
                    self.log_test(f"PHQ-9 {severity} Range (Low)", False, f"Score {test_score} not in range {min_score}-{max_score}")
                    return False
                
                # Test upper boundary
                if max_score <= 27:
                    if min_score <= max_score <= max_score:
                        self.log_test(f"PHQ-9 {severity} Range (High)", True, f"Score {max_score} in range {min_score}-{max_score}")
                    else:
                        self.log_test(f"PHQ-9 {severity} Range (High)", False, f"Score {max_score} not in range {min_score}-{max_score}")
                        return False
            
            return True
            
        except Exception as e:
            self.log_test("PHQ-9 Scoring Scenarios", False, str(e))
            return False
    
    def test_gad7_scoring_scenarios(self):
        """Test GAD-7 scoring with various clinical scenarios"""
        try:
            # Test minimal anxiety (all 0s)
            minimal_scores = {f'q{i}': 0 for i in range(1, 8)}
            total = sum(minimal_scores.values())
            if total == 0:
                self.log_test("GAD-7 Minimal Anxiety", True, f"Score: {total}/21 (Minimal)")
            else:
                self.log_test("GAD-7 Minimal Anxiety", False, f"Expected 0, got {total}")
                return False
            
            # Test mild anxiety (scores of 1)
            mild_scores = {f'q{i}': 1 for i in range(1, 8)}
            total = sum(mild_scores.values())
            if total == 7:
                self.log_test("GAD-7 Mild Anxiety", True, f"Score: {total}/21 (Mild)")
            else:
                self.log_test("GAD-7 Mild Anxiety", False, f"Expected 7, got {total}")
                return False
            
            # Test moderate anxiety (scores of 2)
            moderate_scores = {f'q{i}': 2 for i in range(1, 8)}
            total = sum(moderate_scores.values())
            if total == 14:
                self.log_test("GAD-7 Moderate Anxiety", True, f"Score: {total}/21 (Moderate)")
            else:
                self.log_test("GAD-7 Moderate Anxiety", False, f"Expected 14, got {total}")
                return False
            
            # Test severe anxiety (all 3s)
            severe_scores = {f'q{i}': 3 for i in range(1, 8)}
            total = sum(severe_scores.values())
            if total == 21:
                self.log_test("GAD-7 Severe Anxiety", True, f"Score: {total}/21 (Severe)")
            else:
                self.log_test("GAD-7 Severe Anxiety", False, f"Expected 21, got {total}")
                return False
            
            # Test mixed scores (realistic scenario)
            mixed_scores = {'q1': 2, 'q2': 2, 'q3': 1, 'q4': 3, 'q5': 1, 'q6': 2, 'q7': 1}
            total = sum(mixed_scores.values())
            expected = 12
            if total == expected:
                self.log_test("GAD-7 Mixed Scores", True, f"Score: {total}/21 (Moderate)")
            else:
                self.log_test("GAD-7 Mixed Scores", False, f"Expected {expected}, got {total}")
                return False
            
            # Test clinical interpretation ranges
            interpretations = [
                (0, 4, "Minimal"),
                (5, 9, "Mild"),
                (10, 14, "Moderate"),
                (15, 21, "Severe")
            ]
            
            for min_score, max_score, severity in interpretations:
                # Test boundary values
                test_score = min_score
                if min_score <= test_score <= max_score:
                    self.log_test(f"GAD-7 {severity} Range (Low)", True, f"Score {test_score} in range {min_score}-{max_score}")
                else:
                    self.log_test(f"GAD-7 {severity} Range (Low)", False, f"Score {test_score} not in range {min_score}-{max_score}")
                    return False
                
                # Test upper boundary
                if min_score <= max_score <= max_score:
                    self.log_test(f"GAD-7 {severity} Range (High)", True, f"Score {max_score} in range {min_score}-{max_score}")
                else:
                    self.log_test(f"GAD-7 {severity} Range (High)", False, f"Score {max_score} not in range {min_score}-{max_score}")
                    return False
            
            return True
            
        except Exception as e:
            self.log_test("GAD-7 Scoring Scenarios", False, str(e))
            return False
    
    def test_suicide_risk_assessment_scenarios(self):
        """Test comprehensive suicide risk assessment scenarios"""
        try:
            from streamlit_sql_zai import calculate_suicide_risk_level
            
            # Test Case 1: No risk factors
            no_risk = {
                'suicide': {
                    'current_si': 'None',
                    'previous_attempts': 'None',
                    'means_access': 'No access',
                    'family_suicide': 'No',
                    'substance_use_current': False,
                    'social_isolation': False,
                    'recent_loss': False
                }
            }
            risk_level = calculate_suicide_risk_level(no_risk)
            if risk_level == 'Low':
                self.log_test("Risk Assessment - No Risk Factors", True, f"Risk: {risk_level}")
            else:
                self.log_test("Risk Assessment - No Risk Factors", False, f"Expected Low, got {risk_level}")
                return False
            
            # Test Case 2: Passive ideation only
            passive_risk = {
                'suicide': {
                    'current_si': 'Passive (wish to be dead)',
                    'previous_attempts': 'None',
                    'means_access': 'No access',
                    'family_suicide': 'No'
                }
            }
            risk_level = calculate_suicide_risk_level(passive_risk)
            if risk_level in ['Low', 'Moderate']:
                self.log_test("Risk Assessment - Passive Ideation", True, f"Risk: {risk_level}")
            else:
                self.log_test("Risk Assessment - Passive Ideation", False, f"Expected Low/Moderate, got {risk_level}")
                return False
            
            # Test Case 3: Active ideation without plan
            active_no_plan = {
                'suicide': {
                    'current_si': 'Active ideation without plan',
                    'previous_attempts': 'None',
                    'means_access': 'No access',
                    'family_suicide': 'No'
                }
            }
            risk_level = calculate_suicide_risk_level(active_no_plan)
            if risk_level in ['Moderate', 'High']:
                self.log_test("Risk Assessment - Active No Plan", True, f"Risk: {risk_level}")
            else:
                self.log_test("Risk Assessment - Active No Plan", False, f"Expected Moderate/High, got {risk_level}")
                return False
            
            # Test Case 4: Active ideation with plan
            active_with_plan = {
                'suicide': {
                    'current_si': 'Active ideation with plan',
                    'previous_attempts': 'None',
                    'means_access': 'Some access',
                    'family_suicide': 'No'
                }
            }
            risk_level = calculate_suicide_risk_level(active_with_plan)
            if risk_level in ['High', 'Imminent']:
                self.log_test("Risk Assessment - Active With Plan", True, f"Risk: {risk_level}")
            else:
                self.log_test("Risk Assessment - Active With Plan", False, f"Expected High/Imminent, got {risk_level}")
                return False
            
            # Test Case 5: Multiple risk factors
            multiple_risks = {
                'suicide': {
                    'current_si': 'Active ideation with plan',
                    'previous_attempts': '1 attempt',
                    'means_access': 'Immediate access',
                    'family_suicide': 'Yes - completed',
                    'substance_use_current': True,
                    'social_isolation': True,
                    'recent_loss': True
                }
            }
            risk_level = calculate_suicide_risk_level(multiple_risks)
            if risk_level == 'Imminent':
                self.log_test("Risk Assessment - Multiple Risk Factors", True, f"Risk: {risk_level}")
            else:
                self.log_test("Risk Assessment - Multiple Risk Factors", False, f"Expected Imminent, got {risk_level}")
                return False
            
            # Test Case 6: Previous attempt with current ideation
            previous_attempt = {
                'suicide': {
                    'current_si': 'Active ideation without plan',
                    'previous_attempts': '2-3 attempts',
                    'means_access': 'No access',
                    'family_suicide': 'No'
                }
            }
            risk_level = calculate_suicide_risk_level(previous_attempt)
            if risk_level in ['High', 'Imminent']:
                self.log_test("Risk Assessment - Previous Attempts", True, f"Risk: {risk_level}")
            else:
                self.log_test("Risk Assessment - Previous Attempts", False, f"Expected High/Imminent, got {risk_level}")
                return False
            
            # Test Case 7: Family history impact
            family_history = {
                'suicide': {
                    'current_si': 'Passive (wish to be dead)',
                    'previous_attempts': 'None',
                    'means_access': 'No access',
                    'family_suicide': 'Yes - completed'
                }
            }
            risk_level = calculate_suicide_risk_level(family_history)
            if risk_level in ['Low', 'Moderate']:
                self.log_test("Risk Assessment - Family History", True, f"Risk: {risk_level}")
            else:
                self.log_test("Risk Assessment - Family History", False, f"Expected Low/Moderate, got {risk_level}")
                return False
            
            # Test Case 8: Means access escalation
            means_access = {
                'suicide': {
                    'current_si': 'Active ideation with plan',
                    'previous_attempts': 'None',
                    'means_access': 'Immediate access',
                    'family_suicide': 'No'
                }
            }
            risk_level = calculate_suicide_risk_level(means_access)
            if risk_level in ['High', 'Imminent']:
                self.log_test("Risk Assessment - Immediate Means Access", True, f"Risk: {risk_level}")
            else:
                self.log_test("Risk Assessment - Immediate Means Access", False, f"Expected High/Imminent, got {risk_level}")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("Suicide Risk Assessment Scenarios", False, str(e))
            return False
    
    def test_clinical_decision_support(self):
        """Test clinical decision support recommendations"""
        try:
            # Test PHQ-9 based recommendations
            phq9_recommendations = {
                (0, 4): "Monitor; may not require treatment",
                (5, 9): "Mild depression; watchful waiting, counseling",
                (10, 14): "Moderate depression; counseling, consider medication",
                (15, 19): "Moderately severe depression; medication and counseling",
                (20, 27): "Severe depression; immediate treatment, consider hospitalization"
            }
            
            for (min_score, max_score), recommendation in phq9_recommendations.items():
                test_score = min_score + 1 if min_score < max_score else min_score
                if min_score <= test_score <= max_score:
                    self.log_test(f"PHQ-9 Clinical Decision (Score {test_score})", True, recommendation)
                else:
                    self.log_test(f"PHQ-9 Clinical Decision (Score {test_score})", False, f"Score {test_score} not in range")
                    return False
            
            # Test GAD-7 based recommendations
            gad7_recommendations = {
                (0, 4): "Minimal anxiety; no treatment needed",
                (5, 9): "Mild anxiety; counseling, relaxation techniques",
                (10, 14): "Moderate anxiety; counseling, consider medication",
                (15, 21): "Severe anxiety; medication and therapy recommended"
            }
            
            for (min_score, max_score), recommendation in gad7_recommendations.items():
                test_score = min_score + 1 if min_score < max_score else min_score
                if min_score <= test_score <= max_score:
                    self.log_test(f"GAD-7 Clinical Decision (Score {test_score})", True, recommendation)
                else:
                    self.log_test(f"GAD-7 Clinical Decision (Score {test_score})", False, f"Score {test_score} not in range")
                    return False
            
            # Test risk-based interventions
            risk_interventions = {
                'Low': "Routine follow-up, safety planning",
                'Moderate': "Increased monitoring, safety planning, remove means",
                'High': "Intensive monitoring, consider hospitalization, remove means",
                'Imminent': "Immediate intervention, hospitalization, constant supervision"
            }
            
            for risk_level, intervention in risk_interventions.items():
                self.log_test(f"Risk Intervention ({risk_level})", True, intervention)
            
            return True
            
        except Exception as e:
            self.log_test("Clinical Decision Support", False, str(e))
            return False
    
    def test_edge_cases_and_validation(self):
        """Test edge cases and validation scenarios"""
        try:
            from streamlit_sql_zai import calculate_suicide_risk_level
            
            # Test invalid score ranges
            invalid_phq9_scores = [-1, 28, 50, 100]
            for score in invalid_phq9_scores:
                if not (0 <= score <= 27):
                    self.log_test(f"PHQ-9 Invalid Score Detection ({score})", True, f"Score {score} correctly identified as invalid")
                else:
                    self.log_test(f"PHQ-9 Invalid Score Detection ({score})", False, f"Score {score} should be invalid")
                    return False
            
            invalid_gad7_scores = [-1, 22, 30, 50]
            for score in invalid_gad7_scores:
                if not (0 <= score <= 21):
                    self.log_test(f"GAD-7 Invalid Score Detection ({score})", True, f"Score {score} correctly identified as invalid")
                else:
                    self.log_test(f"GAD-7 Invalid Score Detection ({score})", False, f"Score {score} should be invalid")
                    return False
            
            # Test missing data handling
            incomplete_risk_data = {
                'suicide': {
                    'current_si': 'Active ideation with plan'
                    # Missing other fields
                }
            }
            risk_level = calculate_suicide_risk_level(incomplete_risk_data)
            if risk_level in ['Low', 'Moderate', 'High', 'Imminent']:
                self.log_test("Risk Assessment - Incomplete Data", True, f"Handled gracefully: {risk_level}")
            else:
                self.log_test("Risk Assessment - Incomplete Data", False, f"Invalid result: {risk_level}")
                return False
            
            # Test malformed data
            malformed_data = {
                'suicide': "not a dictionary"
            }
            risk_level = calculate_suicide_risk_level(malformed_data)
            if risk_level in ['Low', 'Moderate']:  # Should default to safe value
                self.log_test("Risk Assessment - Malformed Data", True, f"Safe default: {risk_level}")
            else:
                self.log_test("Risk Assessment - Malformed Data", False, f"Unsafe result: {risk_level}")
                return False
            
            # Test None values
            none_data = None
            risk_level = calculate_suicide_risk_level(none_data)
            if risk_level in ['Low', 'Moderate']:
                self.log_test("Risk Assessment - None Data", True, f"Safe default: {risk_level}")
            else:
                self.log_test("Risk Assessment - None Data", False, f"Unsafe result: {risk_level}")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("Edge Cases and Validation", False, str(e))
            return False
    
    def run_all_tests(self):
        """Run all clinical assessment tests"""
        print("🧪 Starting Clinical Assessment Testing")
        print("=" * 60)
        
        tests = [
            ("PHQ-9 Scoring Scenarios", self.test_phq9_scoring_scenarios),
            ("GAD-7 Scoring Scenarios", self.test_gad7_scoring_scenarios),
            ("Suicide Risk Assessment Scenarios", self.test_suicide_risk_assessment_scenarios),
            ("Clinical Decision Support", self.test_clinical_decision_support),
            ("Edge Cases and Validation", self.test_edge_cases_and_validation)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                self.log_test(test_name, False, f"Exception: {e}")
                failed += 1
        
        print("\n" + "=" * 60)
        print(f"📊 Clinical Assessment Test Results: {passed} passed, {failed} failed")
        
        if failed == 0:
            print("🎉 All clinical assessment tests passed!")
        else:
            print("⚠️ Some clinical assessment tests failed. Please review the issues above.")
        
        return failed == 0

if __name__ == "__main__":
    tester = ClinicalAssessmentTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
