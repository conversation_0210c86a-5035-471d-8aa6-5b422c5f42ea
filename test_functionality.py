#!/usr/bin/env python3
"""
Comprehensive functionality test for the Psychiatric Assessment System
Tests core functionality without requiring Streamlit UI
"""

import sys
import os
import sqlite3
import json
import datetime
import traceback
from typing import Dict, Any, List

# Add current directory to path to import the main module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if all required modules can be imported"""
    print("🔍 Testing imports...")
    try:
        import streamlit_sql_zai as app
        print("✅ Main application module imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import main module: {e}")
        return False
    except Exception as e:
        print(f"❌ Error during import: {e}")
        traceback.print_exc()
        return False

def test_database_functions():
    """Test database creation and basic operations"""
    print("\n🗄️ Testing database functions...")
    
    try:
        # Import after ensuring the module loads
        from streamlit_sql_zai import (
            create_connection, create_tables, validate_database,
            save_patient_data, save_assessment_data, get_all_patients
        )
        
        # Test database connection
        conn = create_connection()
        if conn is None:
            print("❌ Failed to create database connection")
            return False
        conn.close()
        print("✅ Database connection successful")
        
        # Test table creation
        create_tables()
        print("✅ Database tables created successfully")
        
        # Test database validation
        valid, message = validate_database()
        if valid:
            print(f"✅ Database validation passed: {message}")
        else:
            print(f"❌ Database validation failed: {message}")
            return False
        
        # Test basic data operations
        test_patient_data = {
            'demographics': {
                'age': 35,
                'gender': 'Male',
                'sex_assigned': 'Male',
                'marital_status': 'Married',
                'children': '2',
                'education': "Bachelor's degree",
                'occupation': 'Engineer',
                'employment_status': 'Employed full-time',
                'ethnicity': ['White/Caucasian'],
                'living_situation': 'With spouse/partner',
                'housing_stability': 'Stable housing'
            }
        }
        
        # Mock session state for testing
        class MockSessionState:
            def __init__(self):
                self.patient_id = "TEST-001"
                self.assessment_start_time = datetime.datetime.now()
        
        # Create mock session state
        import streamlit_sql_zai
        streamlit_sql_zai.st = type('MockStreamlit', (), {
            'session_state': MockSessionState()
        })()
        
        # Test patient data saving
        result = save_patient_data(test_patient_data)
        if result:
            print("✅ Patient data saved successfully")
        else:
            print("❌ Failed to save patient data")
            return False
        
        # Test retrieving patients
        patients = get_all_patients()
        if not patients.empty:
            print(f"✅ Retrieved {len(patients)} patients from database")
        else:
            print("⚠️ No patients found in database")
        
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        traceback.print_exc()
        return False

def test_clinical_scales():
    """Test clinical scale calculations"""
    print("\n📊 Testing clinical scales...")
    
    try:
        from streamlit_sql_zai import phq9_questions, gad7_questions
        
        # Test PHQ-9 questions
        phq9_q = phq9_questions()
        if len(phq9_q) == 9:
            print("✅ PHQ-9 questions loaded correctly (9 questions)")
        else:
            print(f"❌ PHQ-9 should have 9 questions, found {len(phq9_q)}")
            return False
        
        # Test GAD-7 questions
        gad7_q = gad7_questions()
        if len(gad7_q) == 7:
            print("✅ GAD-7 questions loaded correctly (7 questions)")
        else:
            print(f"❌ GAD-7 should have 7 questions, found {len(gad7_q)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Clinical scales test failed: {e}")
        traceback.print_exc()
        return False

def test_risk_assessment():
    """Test suicide risk assessment logic"""
    print("\n⚠️ Testing risk assessment...")
    
    try:
        from streamlit_sql_zai import calculate_suicide_risk_level
        
        # Test low risk scenario
        low_risk_data = {
            'suicide': {
                'current_si': 'None',
                'previous_attempts': 'None',
                'means_access': 'No access',
                'family_suicide': 'No'
            }
        }
        risk_level = calculate_suicide_risk_level(low_risk_data)
        if risk_level == 'Low':
            print("✅ Low risk assessment correct")
        else:
            print(f"❌ Expected 'Low' risk, got '{risk_level}'")
            return False
        
        # Test high risk scenario
        high_risk_data = {
            'suicide': {
                'current_si': 'Active ideation with plan',
                'previous_attempts': '1 attempt',
                'means_access': 'Immediate access',
                'family_suicide': 'Yes - completed'
            }
        }
        risk_level = calculate_suicide_risk_level(high_risk_data)
        if risk_level in ['High', 'Imminent']:
            print(f"✅ High risk assessment correct: {risk_level}")
        else:
            print(f"❌ Expected 'High' or 'Imminent' risk, got '{risk_level}'")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Risk assessment test failed: {e}")
        traceback.print_exc()
        return False

def test_data_validation():
    """Test data validation functions"""
    print("\n✅ Testing data validation...")
    
    try:
        from streamlit_sql_zai import (
            validate_date, validate_number, validate_required,
            safe_json_dumps, DateTimeEncoder
        )
        
        # Test date validation
        today = datetime.date.today()
        future_date = today + datetime.timedelta(days=1)
        
        error = validate_date(today, allow_future=False)
        if error is None:
            print("✅ Valid date validation passed")
        else:
            print(f"❌ Valid date validation failed: {error}")
            return False
        
        error = validate_date(future_date, allow_future=False)
        if error is not None:
            print("✅ Future date validation correctly rejected")
        else:
            print("❌ Future date validation should have failed")
            return False
        
        # Test number validation
        error = validate_number(25, 18, 100, "Age")
        if error is None:
            print("✅ Valid number validation passed")
        else:
            print(f"❌ Valid number validation failed: {error}")
            return False
        
        error = validate_number(150, 18, 100, "Age")
        if error is not None:
            print("✅ Invalid number validation correctly rejected")
        else:
            print("❌ Invalid number validation should have failed")
            return False
        
        # Test JSON serialization
        test_data = {
            'date': datetime.datetime.now(),
            'text': 'test',
            'number': 42
        }
        
        json_str = safe_json_dumps(test_data)
        if json_str and 'error' not in json_str:
            print("✅ JSON serialization with datetime successful")
        else:
            print(f"❌ JSON serialization failed: {json_str}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Data validation test failed: {e}")
        traceback.print_exc()
        return False

def test_mse_options():
    """Test Mental State Examination options"""
    print("\n🧠 Testing MSE options...")
    
    try:
        from streamlit_sql_zai import mse_options
        
        mse_opts = mse_options()
        required_categories = [
            'appearance', 'behavior', 'speech', 'mood', 'affect',
            'thought_process', 'thought_content', 'perceptions',
            'cognition', 'insight', 'judgment'
        ]
        
        for category in required_categories:
            if category in mse_opts and len(mse_opts[category]) > 0:
                print(f"✅ MSE {category} options available ({len(mse_opts[category])} options)")
            else:
                print(f"❌ MSE {category} options missing or empty")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ MSE options test failed: {e}")
        traceback.print_exc()
        return False

def run_all_tests():
    """Run all functionality tests"""
    print("🚀 Starting Comprehensive Functionality Tests")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("Database Functions", test_database_functions),
        ("Clinical Scales", test_clinical_scales),
        ("Risk Assessment", test_risk_assessment),
        ("Data Validation", test_data_validation),
        ("MSE Options", test_mse_options)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                failed += 1
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! The application core functionality is working correctly.")
    else:
        print("⚠️ Some tests failed. Please review the issues above.")
    
    return failed == 0

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
