#!/usr/bin/env python3
"""
UI Component Testing for Psychiatric Assessment System
Tests UI components and validation logic without requiring Streamlit server
"""

import sys
import os
import datetime
import json
from typing import Dict, Any, List

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class UIComponentTester:
    def __init__(self):
        self.test_results = []
        
    def log_test(self, test_name: str, passed: bool, message: str = ""):
        """Log test results"""
        status = "✅ PASS" if passed else "❌ FAIL"
        self.test_results.append({
            'test': test_name,
            'passed': passed,
            'message': message
        })
        print(f"{status}: {test_name}")
        if message:
            print(f"    {message}")
    
    def test_validation_functions(self):
        """Test validation functions"""
        try:
            from streamlit_sql_zai import (
                validate_date, validate_number, validate_required,
                calculate_suicide_risk_level
            )
            
            # Test date validation
            today = datetime.date.today()
            future_date = today + datetime.timedelta(days=1)
            past_date = today - datetime.timedelta(days=30)
            
            # Valid date should pass
            error = validate_date(today, allow_future=False)
            if error is None:
                self.log_test("Date Validation - Valid Date", True)
            else:
                self.log_test("Date Validation - Valid Date", False, f"Unexpected error: {error}")
                return False
            
            # Future date should fail when not allowed
            error = validate_date(future_date, allow_future=False)
            if error is not None:
                self.log_test("Date Validation - Future Date Rejection", True)
            else:
                self.log_test("Date Validation - Future Date Rejection", False, "Future date should be rejected")
                return False
            
            # Past date should pass
            error = validate_date(past_date, allow_future=False)
            if error is None:
                self.log_test("Date Validation - Past Date", True)
            else:
                self.log_test("Date Validation - Past Date", False, f"Unexpected error: {error}")
                return False
            
            # Test number validation
            valid_age = 35
            invalid_age_low = -5
            invalid_age_high = 200
            
            error = validate_number(valid_age, 0, 150, "Age")
            if error is None:
                self.log_test("Number Validation - Valid Age", True)
            else:
                self.log_test("Number Validation - Valid Age", False, f"Unexpected error: {error}")
                return False
            
            error = validate_number(invalid_age_low, 0, 150, "Age")
            if error is not None:
                self.log_test("Number Validation - Invalid Low Age", True)
            else:
                self.log_test("Number Validation - Invalid Low Age", False, "Low age should be rejected")
                return False
            
            error = validate_number(invalid_age_high, 0, 150, "Age")
            if error is not None:
                self.log_test("Number Validation - Invalid High Age", True)
            else:
                self.log_test("Number Validation - Invalid High Age", False, "High age should be rejected")
                return False
            
            # Test required field validation
            error = validate_required("Valid input", "Test Field")
            if error is None:
                self.log_test("Required Field - Valid Input", True)
            else:
                self.log_test("Required Field - Valid Input", False, f"Unexpected error: {error}")
                return False
            
            error = validate_required("", "Test Field")
            if error is not None:
                self.log_test("Required Field - Empty Input", True)
            else:
                self.log_test("Required Field - Empty Input", False, "Empty input should be rejected")
                return False
            
            error = validate_required(None, "Test Field")
            if error is not None:
                self.log_test("Required Field - None Input", True)
            else:
                self.log_test("Required Field - None Input", False, "None input should be rejected")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("Validation Functions", False, str(e))
            return False
    
    def test_clinical_scales_logic(self):
        """Test clinical scales calculation logic"""
        try:
            from streamlit_sql_zai import phq9_questions, gad7_questions
            
            # Test PHQ-9 questions
            phq9_q = phq9_questions()
            if len(phq9_q) == 9:
                self.log_test("PHQ-9 Questions Count", True, f"Found {len(phq9_q)} questions")
            else:
                self.log_test("PHQ-9 Questions Count", False, f"Expected 9, found {len(phq9_q)}")
                return False
            
            # Test GAD-7 questions
            gad7_q = gad7_questions()
            if len(gad7_q) == 7:
                self.log_test("GAD-7 Questions Count", True, f"Found {len(gad7_q)} questions")
            else:
                self.log_test("GAD-7 Questions Count", False, f"Expected 7, found {len(gad7_q)}")
                return False
            
            # Test score calculations
            # PHQ-9 maximum score test
            max_phq9_score = 9 * 3  # 9 questions, max 3 points each
            if max_phq9_score == 27:
                self.log_test("PHQ-9 Maximum Score", True, f"Max score: {max_phq9_score}")
            else:
                self.log_test("PHQ-9 Maximum Score", False, f"Expected 27, calculated {max_phq9_score}")
                return False
            
            # GAD-7 maximum score test
            max_gad7_score = 7 * 3  # 7 questions, max 3 points each
            if max_gad7_score == 21:
                self.log_test("GAD-7 Maximum Score", True, f"Max score: {max_gad7_score}")
            else:
                self.log_test("GAD-7 Maximum Score", False, f"Expected 21, calculated {max_gad7_score}")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("Clinical Scales Logic", False, str(e))
            return False
    
    def test_risk_assessment_scenarios(self):
        """Test various risk assessment scenarios"""
        try:
            from streamlit_sql_zai import calculate_suicide_risk_level
            
            # Test low risk scenario
            low_risk_data = {
                'suicide': {
                    'current_si': 'None',
                    'previous_attempts': 'None',
                    'means_access': 'No access',
                    'family_suicide': 'No'
                }
            }
            
            risk_level = calculate_suicide_risk_level(low_risk_data)
            if risk_level == 'Low':
                self.log_test("Risk Assessment - Low Risk", True, f"Risk level: {risk_level}")
            else:
                self.log_test("Risk Assessment - Low Risk", False, f"Expected Low, got {risk_level}")
                return False
            
            # Test moderate risk scenario
            moderate_risk_data = {
                'suicide': {
                    'current_si': 'Active ideation without plan',
                    'previous_attempts': 'None',
                    'means_access': 'No access',
                    'family_suicide': 'No'
                }
            }
            
            risk_level = calculate_suicide_risk_level(moderate_risk_data)
            if risk_level in ['Moderate', 'High']:  # Could be either depending on scoring
                self.log_test("Risk Assessment - Moderate Risk", True, f"Risk level: {risk_level}")
            else:
                self.log_test("Risk Assessment - Moderate Risk", False, f"Expected Moderate/High, got {risk_level}")
                return False
            
            # Test high risk scenario
            high_risk_data = {
                'suicide': {
                    'current_si': 'Active ideation with plan',
                    'previous_attempts': '1 attempt',
                    'means_access': 'Immediate access',
                    'family_suicide': 'Yes - completed'
                }
            }
            
            risk_level = calculate_suicide_risk_level(high_risk_data)
            if risk_level in ['High', 'Imminent']:
                self.log_test("Risk Assessment - High Risk", True, f"Risk level: {risk_level}")
            else:
                self.log_test("Risk Assessment - High Risk", False, f"Expected High/Imminent, got {risk_level}")
                return False
            
            # Test invalid data handling
            invalid_data = "not a dictionary"
            risk_level = calculate_suicide_risk_level(invalid_data)
            if risk_level in ['Low', 'Moderate']:  # Should handle gracefully
                self.log_test("Risk Assessment - Invalid Data", True, f"Handled gracefully: {risk_level}")
            else:
                self.log_test("Risk Assessment - Invalid Data", False, f"Unexpected result: {risk_level}")
                return False
            
            # Test empty data
            empty_data = {}
            risk_level = calculate_suicide_risk_level(empty_data)
            if risk_level == 'Low':
                self.log_test("Risk Assessment - Empty Data", True, f"Default to Low: {risk_level}")
            else:
                self.log_test("Risk Assessment - Empty Data", False, f"Expected Low, got {risk_level}")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("Risk Assessment Scenarios", False, str(e))
            return False
    
    def test_mse_options(self):
        """Test Mental State Examination options"""
        try:
            from streamlit_sql_zai import mse_options
            
            mse_opts = mse_options()
            
            # Check if all required categories are present
            required_categories = [
                'appearance', 'behavior', 'speech', 'mood', 'affect',
                'thought_process', 'thought_content', 'perceptions',
                'cognition', 'insight', 'judgment'
            ]
            
            missing_categories = []
            for category in required_categories:
                if category not in mse_opts:
                    missing_categories.append(category)
                elif not isinstance(mse_opts[category], list) or len(mse_opts[category]) == 0:
                    missing_categories.append(f"{category} (empty)")
            
            if not missing_categories:
                self.log_test("MSE Options - All Categories", True, f"Found all {len(required_categories)} categories")
            else:
                self.log_test("MSE Options - All Categories", False, f"Missing: {missing_categories}")
                return False
            
            # Check specific categories have reasonable number of options
            for category in required_categories:
                if category in mse_opts:
                    option_count = len(mse_opts[category])
                    if option_count >= 3:  # At least 3 options per category
                        self.log_test(f"MSE Options - {category.title()}", True, f"{option_count} options")
                    else:
                        self.log_test(f"MSE Options - {category.title()}", False, f"Only {option_count} options")
                        return False
            
            return True
            
        except Exception as e:
            self.log_test("MSE Options", False, str(e))
            return False
    
    def test_json_serialization(self):
        """Test JSON serialization with datetime objects"""
        try:
            from streamlit_sql_zai import safe_json_dumps, DateTimeEncoder
            
            # Test data with datetime objects
            test_data = {
                'date': datetime.datetime.now(),
                'time': datetime.time(14, 30),
                'date_only': datetime.date.today(),
                'string': 'test string',
                'number': 42,
                'list': [1, 2, 3],
                'nested': {
                    'inner_date': datetime.datetime.now(),
                    'inner_string': 'nested'
                }
            }
            
            # Test safe_json_dumps
            json_str = safe_json_dumps(test_data)
            if json_str and 'error' not in json_str.lower():
                self.log_test("JSON Serialization - Complex Data", True, "Serialized successfully")
            else:
                self.log_test("JSON Serialization - Complex Data", False, f"Failed: {json_str}")
                return False
            
            # Test that the JSON can be parsed back
            try:
                parsed_data = json.loads(json_str)
                if isinstance(parsed_data, dict) and 'string' in parsed_data:
                    self.log_test("JSON Deserialization", True, "Parsed successfully")
                else:
                    self.log_test("JSON Deserialization", False, "Parsed data is invalid")
                    return False
            except json.JSONDecodeError as e:
                self.log_test("JSON Deserialization", False, f"Parse error: {e}")
                return False
            
            # Test DateTimeEncoder directly
            encoder = DateTimeEncoder()
            dt_now = datetime.datetime.now()
            encoded = encoder.default(dt_now)
            if isinstance(encoded, str) and 'T' in encoded:  # ISO format
                self.log_test("DateTime Encoder", True, f"Encoded to: {encoded}")
            else:
                self.log_test("DateTime Encoder", False, f"Invalid encoding: {encoded}")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("JSON Serialization", False, str(e))
            return False
    
    def run_all_tests(self):
        """Run all UI component tests"""
        print("🧪 Starting UI Component Testing")
        print("=" * 60)
        
        tests = [
            ("Validation Functions", self.test_validation_functions),
            ("Clinical Scales Logic", self.test_clinical_scales_logic),
            ("Risk Assessment Scenarios", self.test_risk_assessment_scenarios),
            ("MSE Options", self.test_mse_options),
            ("JSON Serialization", self.test_json_serialization)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                self.log_test(test_name, False, f"Exception: {e}")
                failed += 1
        
        print("\n" + "=" * 60)
        print(f"📊 UI Component Test Results: {passed} passed, {failed} failed")
        
        if failed == 0:
            print("🎉 All UI component tests passed!")
        else:
            print("⚠️ Some UI component tests failed. Please review the issues above.")
        
        return failed == 0

if __name__ == "__main__":
    tester = UIComponentTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
