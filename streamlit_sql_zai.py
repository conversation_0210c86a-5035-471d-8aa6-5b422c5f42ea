import streamlit as st
import pandas as pd
import json
import datetime
import uuid
from typing import Dict, Any, List
import plotly.express as px
import plotly.graph_objects as go
import numpy as np
import time
import re
from functools import lru_cache
import sqlite3
from sqlite3 import Error
import os

# Custom JSON encoder to handle date objects
class DateTimeEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles datetime objects"""
    def default(self, obj):
        if isinstance(obj, (datetime.datetime, datetime.date)):
            return obj.isoformat()
        elif isinstance(obj, datetime.time):
            return obj.isoformat()
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        return super().default(obj)

def safe_json_dumps(obj, **kwargs):
    """Safely serialize objects to JSON with custom encoder"""
    try:
        return json.dumps(obj, cls=DateTimeEncoder, **kwargs)
    except (TypeError, ValueError) as e:
        # Fallback: convert problematic objects to strings
        try:
            def convert_to_serializable(item):
                if isinstance(item, (datetime.datetime, datetime.date, datetime.time)):
                    return item.isoformat()
                elif isinstance(item, dict):
                    return {k: convert_to_serializable(v) for k, v in item.items()}
                elif isinstance(item, list):
                    return [convert_to_serializable(i) for i in item]
                elif hasattr(item, '__dict__'):
                    return str(item)
                else:
                    return item
            
            converted_obj = convert_to_serializable(obj)
            return json.dumps(converted_obj, **kwargs)
        except Exception as fallback_error:
            st.error(f"JSON serialization failed: {fallback_error}")
            return json.dumps({"error": "Serialization failed", "original_error": str(e)})

# Configure page
st.set_page_config(
    page_title="Psychiatric Assessment System",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for professional medical interface
st.markdown("""
<style>
    .main-header {
        font-size: 2.8rem;
        font-weight: 800;
        color: #1e3a8a;
        text-align: center;
        margin-bottom: 2rem;
        background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #7c3aed 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .section-header {
        font-size: 1.6rem;
        font-weight: 700;
        color: #1e40af;
        border-left: 5px solid #3b82f6;
        padding-left: 1rem;
        margin: 1.5rem 0;
        background: linear-gradient(90deg, rgba(59,130,246,0.1) 0%, transparent 100%);
        padding: 1rem;
        border-radius: 0 10px 10px 0;
    }
    .subsection-header {
        font-size: 1.2rem;
        font-weight: 600;
        color: #1e40af;
        margin: 1rem 0 0.5rem 0;
        border-bottom: 2px solid #e5e7eb;
        padding-bottom: 0.3rem;
    }
    .metric-card {
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        padding: 1.5rem;
        border-radius: 15px;
        color: white;
        text-align: center;
        margin: 0.5rem 0;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .risk-high { background: linear-gradient(135deg, #dc2626, #b91c1c); }
    .risk-moderate { background: linear-gradient(135deg, #ea580c, #c2410c); }
    .risk-low { background: linear-gradient(135deg, #16a34a, #15803d); }
    
    .stSelectbox > div > div {
        background-color: #f8fafc;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
    }
    .stButton > button {
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        color: white;
        border: none;
        border-radius: 25px;
        padding: 0.7rem 2.5rem;
        font-weight: 700;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }
    .clinical-note {
        background: #fef3c7;
        border-left: 4px solid #f59e0b;
        padding: 1rem;
        margin: 1rem 0;
        border-radius: 0 8px 8px 0;
    }
    .progress-container {
        background: #f1f5f9;
        padding: 1rem;
        border-radius: 10px;
        margin: 1rem 0;
    }
    .validation-error {
        color: #dc2626;
        font-weight: 600;
        margin-top: 0.5rem;
    }
    .auto-save-indicator {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: #10b981;
        color: white;
        padding: 10px 20px;
        border-radius: 25px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        transition: opacity 0.3s ease;
    }
    .template-button {
        background: #e5e7eb;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        padding: 0.3rem 0.8rem;
        margin: 0.2rem;
        font-size: 0.85rem;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    .template-button:hover {
        background: #d1d5db;
    }
    .dashboard-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        margin-bottom: 1rem;
        border-left: 4px solid #3b82f6;
    }
    .patient-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        margin-bottom: 1rem;
        border-left: 4px solid #8b5cf6;
        transition: all 0.3s ease;
    }
    .patient-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    }
    .search-box {
        background: #f8fafc;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        padding: 0.7rem;
        margin-bottom: 1rem;
    }
    .filter-tag {
        display: inline-block;
        background: #e0e7ff;
        color: #4338ca;
        padding: 0.3rem 0.8rem;
        border-radius: 20px;
        margin: 0.2rem;
        font-size: 0.85rem;
    }
</style>
""", unsafe_allow_html=True)

# Database Functions
def create_connection():
    """Create a database connection to the SQLite database with improved error handling"""
    try:
        conn = sqlite3.connect(
            'psychiatric_assessments.db',
            timeout=30.0,  # 30 second timeout to prevent locking
            check_same_thread=False
        )
        # Enable foreign key constraints and WAL mode for better concurrency
        conn.execute("PRAGMA foreign_keys = ON")
        conn.execute("PRAGMA journal_mode = WAL")

        # Fix datetime adapter deprecation warning
        sqlite3.register_adapter(datetime.datetime, lambda val: val.isoformat())
        sqlite3.register_converter("timestamp", lambda val: datetime.datetime.fromisoformat(val.decode()))
        return conn
    except Error as e:
        st.error(f"Database error: {e}")
        return None

def create_tables():
    """Create tables if they don't exist with enhanced constraints"""
    conn = create_connection()
    if conn is not None:
        try:
            cursor = conn.cursor()

            # Enhanced patients table with constraints
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS patients (
                patient_id TEXT PRIMARY KEY,
                age INTEGER CHECK (age >= 0 AND age <= 150),
                gender TEXT,
                sex_assigned TEXT,
                marital_status TEXT,
                children TEXT,
                education TEXT,
                occupation TEXT,
                employment_status TEXT,
                ethnicity TEXT,
                living_situation TEXT,
                housing_stability TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            # Enhanced assessments table with constraints
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS assessments (
                assessment_id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id TEXT NOT NULL,
                assessment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                chief_complaint TEXT,
                history_present_illness TEXT,
                past_psychiatric_history TEXT,
                past_medical_history TEXT,
                family_history TEXT,
                social_developmental_history TEXT,
                substance_use TEXT,
                mental_state_examination TEXT,
                cognitive_assessment TEXT,
                risk_assessment TEXT,
                laboratory_investigations TEXT,
                clinical_scales TEXT,
                diagnostic_formulation TEXT,
                treatment_planning TEXT,
                follow_up_monitoring TEXT,
                suicide_risk_level TEXT CHECK (suicide_risk_level IN ('Low', 'Moderate', 'High', 'Imminent')),
                primary_diagnosis TEXT,
                phq9_score INTEGER CHECK (phq9_score >= 0 AND phq9_score <= 27),
                gad7_score INTEGER CHECK (gad7_score >= 0 AND gad7_score <= 21),
                duration_minutes REAL CHECK (duration_minutes >= 0),
                completion_percentage REAL CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
                data_json TEXT,
                created_by TEXT DEFAULT 'system',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (patient_id) REFERENCES patients (patient_id) ON DELETE CASCADE
            )
            ''')
            
            # Create indexes for faster queries
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_patient_id ON assessments (patient_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_assessment_date ON assessments (assessment_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_primary_diagnosis ON assessments (primary_diagnosis)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_suicide_risk ON assessments (suicide_risk_level)')
            
            conn.commit()
        except Error as e:
            st.error(f"Error creating tables: {e}")
        finally:
            conn.close()

def save_patient_data(patient_data):
    """Save patient demographics to the database"""
    conn = create_connection()
    if conn is not None:
        try:
            cursor = conn.cursor()
            
            # Extract demographics data
            demographics = patient_data.get('demographics', {})
            
            # Insert or replace patient data
            cursor.execute('''
            INSERT OR REPLACE INTO patients (
                patient_id, age, gender, sex_assigned, marital_status, children,
                education, occupation, employment_status, ethnicity, living_situation,
                housing_stability
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                st.session_state.patient_id,
                demographics.get('age'),
                demographics.get('gender'),
                demographics.get('sex_assigned'),
                demographics.get('marital_status'),
                demographics.get('children'),
                demographics.get('education'),
                demographics.get('occupation'),
                demographics.get('employment_status'),
                ', '.join(demographics.get('ethnicity', [])),
                demographics.get('living_situation'),
                demographics.get('housing_stability')
            ))
            
            conn.commit()
            return True
        except Error as e:
            st.error(f"Error saving patient data: {e}")
            return False
        finally:
            conn.close()
    return False

def save_assessment_data(assessment_data):
    """Save assessment data to the database"""
    conn = create_connection()
    if conn is not None:
        try:
            cursor = conn.cursor()
            
            # Extract key ML-relevant fields
            diagnostic_data = assessment_data.get('diagnostic_formulation', {})
            risk_data = assessment_data.get('risk_assessment', {})
            scales_data = assessment_data.get('clinical_scales', {})
            
            # Calculate duration and completion
            duration = (datetime.datetime.now() - st.session_state.assessment_start_time).total_seconds() / 60
            
            # Create mapping between section names and data keys
            section_key_mapping = {
                "Demographics & Identifying Information": "demographics",
                "Chief Complaint & Referral": "chief_complaint",
                "History of Present Illness": "history_present_illness",
                "Past Psychiatric History": "past_psychiatric_history",
                "Past Medical History": "past_medical_history",
                "Family History": "family_history",
                "Social & Developmental History": "social_developmental_history",
                "Substance Use Assessment": "substance_use",
                "Mental State Examination": "mental_state_examination",
                "Cognitive Assessment": "cognitive_assessment",
                "Risk Assessment": "risk_assessment",
                "Laboratory & Investigations": "laboratory_investigations",
                "Clinical Scales & Ratings": "clinical_scales",
                "Diagnostic Formulation": "diagnostic_formulation",
                "Treatment Planning": "treatment_planning",
                "Follow-up & Monitoring": "follow_up_monitoring"
            }
            
            sections = list(section_key_mapping.keys())
            completed = 0
            for section in sections:
                key = section_key_mapping[section]
                if key in assessment_data and assessment_data.get(key):
                    completed += 1
            completion = completed / len(sections) * 100
            
            # Insert assessment data
            cursor.execute('''
            INSERT INTO assessments (
                patient_id, chief_complaint, history_present_illness, past_psychiatric_history,
                past_medical_history, family_history, social_developmental_history, substance_use,
                mental_state_examination, cognitive_assessment, risk_assessment,
                laboratory_investigations, clinical_scales, diagnostic_formulation,
                treatment_planning, follow_up_monitoring, suicide_risk_level,
                primary_diagnosis, phq9_score, gad7_score, duration_minutes,
                completion_percentage, data_json
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                st.session_state.patient_id,
                safe_json_dumps(assessment_data.get('chief_complaint', {})),
                safe_json_dumps(assessment_data.get('history_present_illness', {})),
                safe_json_dumps(assessment_data.get('past_psychiatric_history', {})),
                safe_json_dumps(assessment_data.get('past_medical_history', {})),
                safe_json_dumps(assessment_data.get('family_history', {})),
                safe_json_dumps(assessment_data.get('social_developmental_history', {})),
                safe_json_dumps(assessment_data.get('substance_use', {})),
                safe_json_dumps(assessment_data.get('mental_state_examination', {})),
                safe_json_dumps(assessment_data.get('cognitive_assessment', {})),
                safe_json_dumps(assessment_data.get('risk_assessment', {})),
                safe_json_dumps(assessment_data.get('laboratory_investigations', {})),
                safe_json_dumps(assessment_data.get('clinical_scales', {})),
                safe_json_dumps(assessment_data.get('diagnostic_formulation', {})),
                safe_json_dumps(assessment_data.get('treatment_planning', {})),
                safe_json_dumps(assessment_data.get('follow_up_monitoring', {})),
                risk_data.get('suicide', {}).get('risk_level'),
                diagnostic_data.get('diagnoses', {}).get('primary'),
                scales_data.get('depression', {}).get('phq9_total'),
                scales_data.get('anxiety', {}).get('gad7_total'),
                duration,
                completion,
                safe_json_dumps(assessment_data)
            ))
            
            conn.commit()
            return True
        except Error as e:
            st.error(f"Error saving assessment data: {e}")
            return False
        finally:
            conn.close()
    return False

def get_all_patients():
    """Get all patients from the database"""
    conn = create_connection()
    if conn is not None:
        try:
            patients = pd.read_sql_query("SELECT * FROM patients ORDER BY created_at DESC", conn)
            return patients
        except Error as e:
            st.error(f"Error retrieving patients: {e}")
            return pd.DataFrame()
        finally:
            conn.close()
    return pd.DataFrame()

def get_all_assessments():
    """Get all assessments from the database with patient information"""
    conn = create_connection()
    if conn is not None:
        try:
            query = """
            SELECT a.*, p.age, p.gender, p.marital_status, p.education, p.occupation
            FROM assessments a
            LEFT JOIN patients p ON a.patient_id = p.patient_id
            ORDER BY a.assessment_date DESC
            """
            assessments = pd.read_sql_query(query, conn)
            return assessments
        except Error as e:
            st.error(f"Error retrieving assessments: {e}")
            return pd.DataFrame()
        finally:
            conn.close()
    return pd.DataFrame()

def get_patient_assessments(patient_id):
    """Get all assessments for a specific patient"""
    conn = create_connection()
    if conn is not None:
        try:
            assessments = pd.read_sql_query(
                "SELECT * FROM assessments WHERE patient_id = ? ORDER BY assessment_date DESC", 
                conn, 
                params=(patient_id,)
            )
            return assessments
        except Error as e:
            st.error(f"Error retrieving assessments: {e}")
            return pd.DataFrame()
        finally:
            conn.close()
    return pd.DataFrame()

def get_assessment_by_id(assessment_id):
    """Get a specific assessment by ID with improved error handling"""
    conn = create_connection()
    if conn is not None:
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM assessments WHERE assessment_id = ?", (assessment_id,))
            assessment = cursor.fetchone()

            if assessment:
                columns = [desc[0] for desc in cursor.description]
                assessment_dict = dict(zip(columns, assessment))

                # Fix JSON parsing issue - ensure data_json is a string
                if 'data_json' in assessment_dict and assessment_dict['data_json'] is not None:
                    if not isinstance(assessment_dict['data_json'], str):
                        assessment_dict['data_json'] = str(assessment_dict['data_json'])

                return assessment_dict
            return None
        except Error as e:
            st.error(f"Error retrieving assessment: {e}")
            return None
        finally:
            conn.close()
    return None

def update_assessment(assessment_id, assessment_data):
    """Update an existing assessment"""
    conn = create_connection()
    if conn is not None:
        try:
            cursor = conn.cursor()
            
            # Extract key ML-relevant fields
            diagnostic_data = assessment_data.get('diagnostic_formulation', {})
            risk_data = assessment_data.get('risk_assessment', {})
            scales_data = assessment_data.get('clinical_scales', {})
            
            # Calculate duration and completion
            duration = (datetime.datetime.now() - st.session_state.assessment_start_time).total_seconds() / 60
            
            # Create mapping between section names and data keys
            section_key_mapping = {
                "Demographics & Identifying Information": "demographics",
                "Chief Complaint & Referral": "chief_complaint",
                "History of Present Illness": "history_present_illness",
                "Past Psychiatric History": "past_psychiatric_history",
                "Past Medical History": "past_medical_history",
                "Family History": "family_history",
                "Social & Developmental History": "social_developmental_history",
                "Substance Use Assessment": "substance_use",
                "Mental State Examination": "mental_state_examination",
                "Cognitive Assessment": "cognitive_assessment",
                "Risk Assessment": "risk_assessment",
                "Laboratory & Investigations": "laboratory_investigations",
                "Clinical Scales & Ratings": "clinical_scales",
                "Diagnostic Formulation": "diagnostic_formulation",
                "Treatment Planning": "treatment_planning",
                "Follow-up & Monitoring": "follow_up_monitoring"
            }
            
            sections = list(section_key_mapping.keys())
            completed = 0
            for section in sections:
                key = section_key_mapping[section]
                if key in assessment_data and assessment_data.get(key):
                    completed += 1
            completion = completed / len(sections) * 100
            
            # Update assessment data
            cursor.execute('''
            UPDATE assessments SET
                chief_complaint = ?,
                history_present_illness = ?,
                past_psychiatric_history = ?,
                past_medical_history = ?,
                family_history = ?,
                social_developmental_history = ?,
                substance_use = ?,
                mental_state_examination = ?,
                cognitive_assessment = ?,
                risk_assessment = ?,
                laboratory_investigations = ?,
                clinical_scales = ?,
                diagnostic_formulation = ?,
                treatment_planning = ?,
                follow_up_monitoring = ?,
                suicide_risk_level = ?,
                primary_diagnosis = ?,
                phq9_score = ?,
                gad7_score = ?,
                duration_minutes = ?,
                completion_percentage = ?,
                data_json = ?
            WHERE assessment_id = ?
            ''', (
                safe_json_dumps(assessment_data.get('chief_complaint', {})),
                safe_json_dumps(assessment_data.get('history_present_illness', {})),
                safe_json_dumps(assessment_data.get('past_psychiatric_history', {})),
                safe_json_dumps(assessment_data.get('past_medical_history', {})),
                safe_json_dumps(assessment_data.get('family_history', {})),
                safe_json_dumps(assessment_data.get('social_developmental_history', {})),
                safe_json_dumps(assessment_data.get('substance_use', {})),
                safe_json_dumps(assessment_data.get('mental_state_examination', {})),
                safe_json_dumps(assessment_data.get('cognitive_assessment', {})),
                safe_json_dumps(assessment_data.get('risk_assessment', {})),
                safe_json_dumps(assessment_data.get('laboratory_investigations', {})),
                safe_json_dumps(assessment_data.get('clinical_scales', {})),
                safe_json_dumps(assessment_data.get('diagnostic_formulation', {})),
                safe_json_dumps(assessment_data.get('treatment_planning', {})),
                safe_json_dumps(assessment_data.get('follow_up_monitoring', {})),
                risk_data.get('suicide', {}).get('risk_level'),
                diagnostic_data.get('diagnoses', {}).get('primary'),
                scales_data.get('depression', {}).get('phq9_total'),
                scales_data.get('anxiety', {}).get('gad7_total'),
                duration,
                completion,
                safe_json_dumps(assessment_data),
                assessment_id
            ))
            
            conn.commit()
            return True
        except Error as e:
            st.error(f"Error updating assessment: {e}")
            return False
        finally:
            conn.close()
    return False

def delete_assessment(assessment_id):
    """Delete an assessment from the database"""
    conn = create_connection()
    if conn is not None:
        try:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM assessments WHERE assessment_id = ?", (assessment_id,))
            conn.commit()
            return True
        except Error as e:
            st.error(f"Error deleting assessment: {e}")
            return False
        finally:
            conn.close()
    return False

def get_ml_dataset():
    """Get a dataset optimized for ML training"""
    conn = create_connection()
    if conn is not None:
        try:
            # Query to get ML-relevant data
            query = '''
            SELECT 
                p.patient_id, p.age, p.gender, p.sex_assigned, p.marital_status, 
                p.children, p.education, p.occupation, p.employment_status, 
                p.ethnicity, p.living_situation, p.housing_stability,
                a.assessment_date, a.suicide_risk_level, a.primary_diagnosis, 
                a.phq9_score, a.gad7_score, a.duration_minutes, a.completion_percentage,
                a.data_json
            FROM assessments a
            JOIN patients p ON a.patient_id = p.patient_id
            ORDER BY a.assessment_date DESC
            '''
            
            df = pd.read_sql_query(query, conn)
            
            # Process JSON data to extract additional features
            if not df.empty and 'data_json' in df.columns:
                # Initialize empty DataFrames for each category
                substance_df = pd.DataFrame()
                risk_df = pd.DataFrame()
                diagnostic_df = pd.DataFrame()
                
                for idx, row in df.iterrows():
                    try:
                        data = json.loads(row['data_json'])
                        
                        # Extract substance use data
                        substance_data = data.get('substance_use', {})
                        if substance_data:
                            substance_row = pd.json_normalize(substance_data)
                            substance_row.index = [idx]
                            substance_df = pd.concat([substance_df, substance_row])
                        
                        # Extract risk assessment data
                        risk_data = data.get('risk_assessment', {})
                        if risk_data:
                            risk_row = pd.json_normalize(risk_data)
                            risk_row.index = [idx]
                            risk_df = pd.concat([risk_df, risk_row])
                        
                        # Extract diagnostic data
                        diagnostic_data = data.get('diagnostic_formulation', {})
                        if diagnostic_data:
                            diagnostic_row = pd.json_normalize(diagnostic_data)
                            diagnostic_row.index = [idx]
                            diagnostic_df = pd.concat([diagnostic_df, diagnostic_row])
                    except Exception as e:
                        st.warning(f"Error processing JSON for patient {row['patient_id']}: {e}")
                        continue
                
                # Prefix columns to avoid conflicts
                if not substance_df.empty:
                    substance_df.columns = ['substance_' + col for col in substance_df.columns]
                
                if not risk_df.empty:
                    risk_df.columns = ['risk_' + col for col in risk_df.columns]
                
                if not diagnostic_df.empty:
                    diagnostic_df.columns = ['diagnostic_' + col for col in diagnostic_df.columns]
                
                # Combine all data
                df = df.drop('data_json', axis=1)
                df = pd.concat([df, substance_df, risk_df, diagnostic_df], axis=1)
            
            return df
        except Error as e:
            st.error(f"Error retrieving ML dataset: {e}")
            return pd.DataFrame()
        finally:
            conn.close()
    return pd.DataFrame()

def validate_database():
    """Validate database integrity and test functions"""
    conn = create_connection()
    if conn is not None:
        try:
            cursor = conn.cursor()
            
            # Test 1: Check if tables exist
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [table[0] for table in cursor.fetchall()]
            
            if 'patients' not in tables or 'assessments' not in tables:
                return False, "Required tables not found"
            
            # Test 2: Check if required columns exist
            cursor.execute("PRAGMA table_info(patients)")
            patient_columns = [column[1] for column in cursor.fetchall()]
            
            required_patient_columns = ['patient_id', 'age', 'gender', 'created_at']
            for col in required_patient_columns:
                if col not in patient_columns:
                    return False, f"Missing column in patients table: {col}"
            
            cursor.execute("PRAGMA table_info(assessments)")
            assessment_columns = [column[1] for column in cursor.fetchall()]
            
            required_assessment_columns = ['assessment_id', 'patient_id', 'assessment_date', 'data_json']
            for col in required_assessment_columns:
                if col not in assessment_columns:
                    return False, f"Missing column in assessments table: {col}"
            
            # Test 3: Insert and retrieve test data
            test_patient_id = "TEST-1234"
            
            # Insert test patient
            cursor.execute('''
            INSERT OR REPLACE INTO patients (
                patient_id, age, gender, sex_assigned, marital_status, children,
                education, occupation, employment_status, ethnicity, living_situation,
                housing_stability
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                test_patient_id, 35, "Male", "Male", "Married", "2",
                "Bachelor's degree", "Engineer", "Employed full-time", "White/Caucasian", 
                "With spouse/partner", "Stable housing"
            ))
            
            # Insert test assessment
            cursor.execute('''
            INSERT INTO assessments (
                patient_id, chief_complaint, history_present_illness, suicide_risk_level,
                primary_diagnosis, phq9_score, gad7_score, duration_minutes,
                completion_percentage, data_json
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                test_patient_id, 
                safe_json_dumps({"complaint": "Depression"}),
                safe_json_dumps({"history": "Chronic depression"}),
                "Moderate",
                "Major Depressive Disorder",
                15,
                12,
                45.2,
                85.0,
                safe_json_dumps({"test": "data"})
            ))
            
            # Retrieve test data
            cursor.execute("SELECT * FROM patients WHERE patient_id = ?", (test_patient_id,))
            test_patient = cursor.fetchone()
            
            cursor.execute("SELECT * FROM assessments WHERE patient_id = ?", (test_patient_id,))
            test_assessment = cursor.fetchone()
            
            # Clean up test data
            cursor.execute("DELETE FROM patients WHERE patient_id = ?", (test_patient_id,))
            cursor.execute("DELETE FROM assessments WHERE patient_id = ?", (test_patient_id,))
            
            conn.commit()
            
            if test_patient is None or test_assessment is None:
                return False, "Failed to insert or retrieve test data"
            
            return True, "Database validation successful"
        except Error as e:
            return False, f"Database validation error: {e}"
        finally:
            conn.close()
    return False, "Failed to connect to database"

# Initialize comprehensive session state with validation
def initialize_session_state():
    """Initialize all necessary session state variables with defaults"""
    defaults = {
        'patient_data': {},
        'current_section': 0,
        'patient_id': f"PSY-{str(uuid.uuid4())[:8].upper()}",
        'assessment_start_time': datetime.datetime.now(),
        'last_auto_save': time.time(),
        'auto_save_enabled': True,
        'section_states': {},  # Track expanded/collapsed state of sections
        'validation_errors': {},
        'substance_details': {},  # Track substance use details
        'assessment_history': [],  # Track multiple assessments
        'current_assessment_index': 0,
        'templates': {
            'depression': "Patient reports persistent low mood, anhedonia, and fatigue. Symptoms interfere with daily functioning.",
            'anxiety': "Patient reports excessive worry, restlessness, and physical symptoms of anxiety including palpitations and shortness of breath.",
            'psychosis': "Patient reports auditory hallucinations and paranoid ideation. Thought process is disorganized with loose associations.",
            'substance_use': "Patient acknowledges substance use with negative consequences including relationship and occupational problems.",
            'trauma': "Patient reports history of trauma with current symptoms including hypervigilance, nightmares, and avoidance behaviors."
        },
        'db_initialized': False,
        'current_view': 'assessment',  # 'assessment' or 'dashboard'
        'editing_assessment_id': None,
        'patient_code_entered': False
    }
    
    for key, value in defaults.items():
        if key not in st.session_state:
            st.session_state[key] = value

initialize_session_state()

# Initialize database if not already done
if not st.session_state.db_initialized:
    create_tables()
    st.session_state.db_initialized = True

# Clinical scales and questionnaires
def phq9_questions():
    return [
        "Little interest or pleasure in doing things",
        "Feeling down, depressed, or hopeless", 
        "Trouble falling or staying asleep, or sleeping too much",
        "Feeling tired or having little energy",
        "Poor appetite or overeating",
        "Feeling bad about yourself or that you are a failure",
        "Trouble concentrating on things",
        "Moving or speaking slowly or being fidgety/restless",
        "Thoughts that you would be better off dead or hurting yourself"
    ]

def gad7_questions():
    return [
        "Feeling nervous, anxious, or on edge",
        "Not being able to stop or control worrying",
        "Worrying too much about different things", 
        "Trouble relaxing",
        "Being so restless that it is hard to sit still",
        "Becoming easily annoyed or irritable",
        "Feeling afraid, as if something awful might happen"
    ]

def mse_options():
    return {
        'appearance': ['Well-groomed', 'Unkempt', 'Bizarre dress', 'Age-appropriate', 'Malnourished', 'Obese'],
        'behavior': ['Cooperative', 'Agitated', 'Withdrawn', 'Hostile', 'Psychomotor retardation', 'Psychomotor agitation', 'Catatonic'],
        'speech': ['Normal rate/rhythm', 'Pressured', 'Slow', 'Loud', 'Soft', 'Monotone', 'Dysarthric', 'Stuttering'],
        'mood': ['Euthymic', 'Depressed', 'Elevated', 'Irritable', 'Anxious', 'Angry', 'Euphoric', 'Dysthymic'],
        'affect': ['Appropriate', 'Inappropriate', 'Constricted', 'Blunted', 'Flat', 'Labile', 'Expansive'],
        'thought_process': ['Linear', 'Tangential', 'Circumstantial', 'Flight of ideas', 'Loose associations', 'Thought blocking', 'Perseveration'],
        'thought_content': ['No abnormalities', 'Delusions', 'Obsessions', 'Compulsions', 'Phobias', 'Suicidal ideation', 'Homicidal ideation'],
        'perceptions': ['No abnormalities', 'Auditory hallucinations', 'Visual hallucinations', 'Tactile hallucinations', 'Olfactory hallucinations', 'Illusions'],
        'cognition': ['Intact', 'Impaired attention', 'Memory impairment', 'Disoriented', 'Abstract thinking impaired', 'Poor judgment'],
        'insight': ['Good', 'Fair', 'Poor', 'Absent', 'Partial'],
        'judgment': ['Good', 'Fair', 'Poor', 'Impaired']
    }

# Helper functions for validation and data management
def validate_date(date_input, allow_future=False, field_name="Date"):
    """Validate date input and return error message if invalid"""
    if date_input is None:
        return f"{field_name} is required"
    
    today = datetime.date.today()
    if not allow_future and date_input > today:
        return f"{field_name} cannot be in the future"
    
    return None

def validate_number(value, min_val, max_val, field_name="Value"):
    """Validate numeric input and return error message if invalid"""
    if value is None:
        return f"{field_name} is required"
    
    if not min_val <= value <= max_val:
        return f"{field_name} must be between {min_val} and {max_val}"
    
    return None

def validate_required(value, field_name="Field"):
    """Validate required field and return error message if empty"""
    if value is None or value == "":
        return f"{field_name} is required"
    return None

def calculate_suicide_risk_level(risk_data):
    """Calculate suicide risk level based on assessment data with improved logic"""
    try:
        if not isinstance(risk_data, dict):
            return 'Low'  # Default to low risk if invalid data

        suicide_data = risk_data.get('suicide', {})
        if not isinstance(suicide_data, dict):
            return 'Low'

        # Initialize risk score for more precise calculation
        risk_score = 0

        # Current suicidal ideation (highest weight)
        current_si = suicide_data.get('current_si', 'None')
        si_scores = {
            'None': 0,
            'Passive (wish to be dead)': 1,
            'Active ideation without plan': 2,
            'Active ideation with plan': 3,
            'Active ideation with intent': 4
        }
        risk_score += si_scores.get(current_si, 0) * 3

        # Previous attempts
        previous_attempts = suicide_data.get('previous_attempts', 'None')
        if previous_attempts and previous_attempts != 'None':
            risk_score += 2

        # Means access
        means_access = suicide_data.get('means_access', 'No access')
        if means_access == 'Immediate access':
            risk_score += 2
        elif means_access == 'Some access':
            risk_score += 1

        # Family history
        family_suicide = suicide_data.get('family_suicide', 'No')
        if family_suicide == 'Yes - completed':
            risk_score += 2
        elif family_suicide == 'Yes - attempted':
            risk_score += 1

        # Additional risk factors
        if suicide_data.get('substance_use_current', False):
            risk_score += 1
        if suicide_data.get('social_isolation', False):
            risk_score += 1
        if suicide_data.get('recent_loss', False):
            risk_score += 1

        # Convert score to risk level
        if risk_score >= 12:
            return 'Imminent'
        elif risk_score >= 8:
            return 'High'
        elif risk_score >= 4:
            return 'Moderate'
        else:
            return 'Low'

    except Exception as e:
        # Log error and return moderate risk as safe default
        st.warning(f"Error calculating suicide risk: {e}")
        return 'Moderate'

def auto_save_progress():
    """Auto-save progress every 30 seconds"""
    if st.session_state.auto_save_enabled:
        current_time = time.time()
        if current_time - st.session_state.last_auto_save > 30:
            try:
                # Save patient demographics
                save_patient_data(st.session_state.patient_data)
                
                # Save assessment data
                if st.session_state.editing_assessment_id:
                    # Update existing assessment
                    update_assessment(st.session_state.editing_assessment_id, st.session_state.patient_data)
                else:
                    # Create new assessment
                    save_assessment_data(st.session_state.patient_data)
                
                st.session_state.last_auto_save = current_time
                st.session_state['auto_save_indicator'] = True
                return True
            except Exception as e:
                st.error(f"Auto-save failed: {e}")
                return False
    return False

def get_template_text(template_type):
    """Get template text for common clinical findings"""
    return st.session_state.templates.get(template_type, "")

def insert_template(template_type, text_area_key):
    """Insert template text into a text area"""
    template = get_template_text(template_type)
    if template and text_area_key in st.session_state:
        current_text = st.session_state[text_area_key]
        if current_text:
            new_text = current_text + "\n\n" + template
        else:
            new_text = template
        st.session_state[text_area_key] = new_text
        st.rerun()

def save_current_assessment():
    """Save current assessment to database"""
    if st.session_state.patient_data:
        # Save patient demographics
        save_patient_data(st.session_state.patient_data)
        
        # Save assessment data
        if st.session_state.editing_assessment_id:
            # Update existing assessment
            update_assessment(st.session_state.editing_assessment_id, st.session_state.patient_data)
            st.success(f"Assessment updated successfully!")
        else:
            # Create new assessment
            save_assessment_data(st.session_state.patient_data)
            st.success(f"Assessment saved successfully!")
        
        # Reset editing state
        st.session_state.editing_assessment_id = None

def load_assessment(assessment_id):
    """Load assessment from database"""
    assessment_data = get_assessment_by_id(assessment_id)
    if assessment_data:
        # Extract patient ID
        patient_id = assessment_data['patient_id']
        st.session_state.patient_id = patient_id
        
        # Load assessment data
        data_json = json.loads(assessment_data['data_json'])
        st.session_state.patient_data = data_json
        st.session_state.editing_assessment_id = assessment_id
        
        # Set assessment start time
        st.session_state.assessment_start_time = datetime.datetime.now()
        
        st.success(f"Loaded assessment for {patient_id}")
        st.session_state.current_view = 'assessment'
        st.rerun()

def new_assessment():
    """Start a new assessment"""
    st.session_state.patient_data = {}
    st.session_state.patient_id = None  # Will be set by user input
    st.session_state.current_section = 0
    st.session_state.editing_assessment_id = None
    st.session_state.current_view = 'assessment'
    st.session_state.patient_code_entered = False
    st.success("New assessment started!")
    st.rerun()

# Navigation
def show_assessment_view():
    """Display the assessment form"""
    
    # Handle patient code input first (before any navigation)
    if not st.session_state.patient_id:
        st.markdown('<h1 class="main-header">🧠 New Psychiatric Assessment</h1>', unsafe_allow_html=True)
        st.markdown('<div class="section-header">Patient Code Entry</div>', unsafe_allow_html=True)
        st.markdown("Please enter a patient code to identify this case (no real names or personal identifiers):")
        
        col1, col2 = st.columns([3, 1])
        with col1:
            patient_code = st.text_input("Patient Code", placeholder="e.g., CASE-001, PT-ABC123", key="patient_code_input")
        with col2:
            if st.button("Set Code", type="primary"):
                if patient_code.strip():
                    st.session_state.patient_id = patient_code.strip().upper()
                    st.session_state.patient_code_entered = True
                    st.success(f"Patient code set: {st.session_state.patient_id}")
                    st.rerun()
                else:
                    st.error("Please enter a valid patient code")
        
        st.info("💡 Use a unique code that helps you identify this case later (e.g., CASE-001, PT-ABC123)")
        return  # Exit function until patient code is set
    
    # Sidebar navigation with enhanced progress tracking
    with st.sidebar:
        st.markdown("### 🧠 Psychiatric Assessment Navigation")
        st.markdown(f"**Patient Code:** `{st.session_state.patient_id}`")
        st.markdown("---")
        
        sections = [
            "Demographics & Identifying Information",
            "Chief Complaint & Referral", 
            "History of Present Illness",
            "Past Psychiatric History",
            "Past Medical History", 
            "Family History",
            "Social & Developmental History",
            "Substance Use Assessment",
            "Mental State Examination",
            "Cognitive Assessment", 
            "Risk Assessment",
            "Laboratory & Investigations",
            "Clinical Scales & Ratings",
            "Diagnostic Formulation",
            "Treatment Planning",
            "Follow-up & Monitoring"
        ]
        
        current_section = st.selectbox("Current Section", sections, 
                                     index=st.session_state.current_section)
        st.session_state.current_section = sections.index(current_section)
        
        # Enhanced progress tracking
        progress = (st.session_state.current_section + 1) / len(sections)
        st.markdown('<div class="progress-container">', unsafe_allow_html=True)
        st.progress(progress)
        st.write(f"**Progress:** {progress:.0%} ({st.session_state.current_section + 1}/{len(sections)})")
        st.markdown('</div>', unsafe_allow_html=True)
        
        # Assessment metadata
        st.markdown("---")
        st.markdown(f"**Assessment Date:** {datetime.datetime.now().strftime('%Y-%m-%d %H:%M')}")
        duration = datetime.datetime.now() - st.session_state.assessment_start_time
        st.markdown(f"**Duration:** {str(duration).split('.')[0]}")
        
        # Data completeness indicator
        completed_sections = len([k for k in st.session_state.patient_data.keys() if st.session_state.patient_data.get(k)])
        st.metric("Completed Sections", f"{completed_sections}/{len(sections)}")
        
        # Assessment management
        st.markdown("---")
        st.markdown("### 📁 Assessment Management")
        
        if st.button("🆕 New Assessment"):
            new_assessment()
        
        if st.button("📊 View Dashboard"):
            st.session_state.current_view = 'dashboard'
            st.rerun()
        
        # Auto-save toggle
        auto_save_enabled = st.checkbox("Enable Auto-save", value=st.session_state.auto_save_enabled)
        st.session_state.auto_save_enabled = auto_save_enabled

    # Main header
    st.markdown('<h1 class="main-header">🧠 Comprehensive Psychiatric Assessment System</h1>', unsafe_allow_html=True)

    # Auto-save indicator
    if 'auto_save_indicator' in st.session_state and st.session_state.auto_save_indicator:
        st.markdown('<div class="auto-save-indicator">✅ Auto-saved</div>', unsafe_allow_html=True)
        st.session_state.auto_save_indicator = False

    # Main assessment header
    st.markdown('<h1 class="main-header">🧠 Psychiatric Assessment System</h1>', unsafe_allow_html=True)

    # Section 0: Demographics & Identifying Information (Simplified for ML)
    if st.session_state.current_section == 0:
        st.markdown('<div class="section-header">Demographics & Identifying Information</div>', unsafe_allow_html=True)
        st.info(f"Patient Code: **{st.session_state.patient_id}**")
        
        # Validation errors display
        if 'demographics_errors' in st.session_state.validation_errors:
            for error in st.session_state.validation_errors['demographics_errors']:
                st.error(error)
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.markdown('<div class="subsection-header">Basic Demographics</div>', unsafe_allow_html=True)
            # Changed default to None to avoid bias in ML
            age = st.number_input("Age", min_value=1, max_value=120, value=None)
            gender = st.selectbox("Gender Identity", 
                                 ["Male", "Female", "Non-binary", "Transgender male", "Transgender female", "Other", "Prefer not to disclose"])
            sex_assigned = st.selectbox("Sex Assigned at Birth", ["Male", "Female", "Intersex", "Prefer not to disclose"])
            
            marital_status = st.selectbox("Marital Status", 
                                        ["Single, never married", "Married", "Divorced", "Widowed", 
                                         "Separated", "Cohabiting/Domestic partnership", "Other"])
            
            children = st.selectbox("Children", ["None", "1", "2", "3", "4+"])
            
        with col2:
            st.markdown('<div class="subsection-header">Socioeconomic Status</div>', unsafe_allow_html=True)
            education = st.selectbox("Highest Education Level", 
                                   ["Less than high school", "High school/GED", "Some college", 
                                    "Associate degree", "Bachelor's degree", "Master's degree", 
                                    "Doctoral degree", "Professional degree"])
            
            occupation = st.text_input("Current/Most Recent Occupation")
            employment_status = st.selectbox("Employment Status", 
                                           ["Employed full-time", "Employed part-time", "Unemployed", 
                                            "Student", "Retired", "Disabled/Unable to work", "Homemaker"])
            
            # Removed income level and insurance as non-essential for ML
            
        with col3:
            st.markdown('<div class="subsection-header">Cultural & Background</div>', unsafe_allow_html=True)
            ethnicity = st.multiselect("Race/Ethnicity (select all that apply)", 
                                     ["White/Caucasian", "Black/African American", "Hispanic/Latino", 
                                      "Asian", "Native American/Alaska Native", "Pacific Islander", 
                                      "Middle Eastern", "Mixed race", "Other"])
            
            # Removed language, interpreter needed, and religion as non-essential for ML
            
            living_situation = st.selectbox("Current Living Situation", 
                                          ["Lives alone", "With spouse/partner", "With family", "With roommates",
                                           "Assisted living", "Group home", "Homeless", "Incarcerated", "Other"])
            
            housing_stability = st.selectbox("Housing Stability", 
                                           ["Stable housing", "Temporary housing", "Frequent moves", "Homeless"])
        
        # Emergency contacts
        st.markdown('<div class="subsection-header">Emergency Contact Information</div>', unsafe_allow_html=True)
        col1, col2 = st.columns(2)
        with col1:
            emergency_contact_name = st.text_input("Emergency Contact Name")
            emergency_contact_relationship = st.text_input("Relationship to Patient")
        with col2:
            emergency_contact_phone = st.text_input("Emergency Contact Phone")
            emergency_contact_address = st.text_area("Emergency Contact Address", height=100)
        
        # Validation before storing
        errors = []
        # Only validate if age is provided (not None)
        if age is not None:
            errors.append(validate_number(age, 1, 120, "Age"))
        # Emergency contact is optional
        if emergency_contact_name:
            errors.append(validate_required(emergency_contact_phone, "Emergency contact phone (required if name provided)"))
        
        # Remove None values
        errors = [e for e in errors if e is not None]
        
        if errors:
            st.session_state.validation_errors['demographics_errors'] = errors
        else:
            if 'demographics_errors' in st.session_state.validation_errors:
                del st.session_state.validation_errors['demographics_errors']
            
            # Store data
            st.session_state.patient_data['demographics'] = {
                'age': age, 'gender': gender, 'sex_assigned': sex_assigned, 'marital_status': marital_status,
                'children': children, 'education': education, 'occupation': occupation, 
                'employment_status': employment_status,
                'ethnicity': ethnicity, 'living_situation': living_situation, 'housing_stability': housing_stability,
                'emergency_contact': {
                    'name': emergency_contact_name, 'relationship': emergency_contact_relationship,
                    'phone': emergency_contact_phone, 'address': emergency_contact_address
                }
            }

    # Section 1: Chief Complaint & Referral
    elif st.session_state.current_section == 1:
        st.markdown('<div class="section-header">Chief Complaint & Referral</div>', unsafe_allow_html=True)
        
        if 'chief_complaint' not in st.session_state.patient_data:
            st.session_state.patient_data['chief_complaint'] = {}
        
        chief_complaint = st.session_state.patient_data['chief_complaint']
        
        # Common Chief Complaints
        st.markdown("### 🎯 Common Chief Complaints (Select all that apply)")
        chief_complaint['common_complaints'] = st.multiselect(
            "Select Common Complaints",
            ["Depression", "Anxiety", "Panic attacks", "Mood swings", "Sleep problems", 
             "Concentration problems", "Memory problems", "Hearing voices", "Paranoid thoughts", 
             "Suicidal thoughts", "Self-harm", "Substance use problems", "Relationship problems", 
             "Work/school stress", "Grief/loss", "Trauma symptoms", "Eating problems", 
             "Anger management", "Social anxiety", "Phobias", "Obsessive thoughts", 
             "Compulsive behaviors", "Medication management", "Second opinion"],
            default=chief_complaint.get('common_complaints', [])
        )
        
        # Patient's Own Words
        chief_complaint['presenting_problem'] = st.text_area(
            "Chief Complaint (in patient's own words)",
            value=chief_complaint.get('presenting_problem', ''),
            height=100,
            help="What brought the patient to seek help today? Use patient's exact words when possible."
        )
        
        # Timeline and Context
        st.markdown("### ⏰ Timeline and Context")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            chief_complaint['duration'] = st.selectbox(
                "Duration of Current Problem",
                ["", "Hours", "Days", "1-2 weeks", "2-4 weeks", "1-3 months", "3-6 months", "6-12 months", "1-2 years", "More than 2 years"],
                index=0 if not chief_complaint.get('duration') else ["", "Hours", "Days", "1-2 weeks", "2-4 weeks", "1-3 months", "3-6 months", "6-12 months", "1-2 years", "More than 2 years"].index(chief_complaint.get('duration'))
            )
            
            chief_complaint['onset_type'] = st.selectbox(
                "Onset Type",
                ["", "Gradual", "Sudden", "Following specific event", "Seasonal pattern", "Unknown"],
                index=0 if not chief_complaint.get('onset_type') else ["", "Gradual", "Sudden", "Following specific event", "Seasonal pattern", "Unknown"].index(chief_complaint.get('onset_type'))
            )
        
        with col2:
            chief_complaint['severity_change'] = st.selectbox(
                "Severity Over Time",
                ["", "Getting worse", "Getting better", "Staying the same", "Fluctuating", "Episodic"],
                index=0 if not chief_complaint.get('severity_change') else ["", "Getting worse", "Getting better", "Staying the same", "Fluctuating", "Episodic"].index(chief_complaint.get('severity_change'))
            )
            
            chief_complaint['current_severity'] = st.selectbox(
                "Current Severity (1-10 scale)",
                ["", "1-2 (Mild)", "3-4 (Mild-Moderate)", "5-6 (Moderate)", "7-8 (Severe)", "9-10 (Extreme)"],
                index=0 if not chief_complaint.get('current_severity') else ["", "1-2 (Mild)", "3-4 (Mild-Moderate)", "5-6 (Moderate)", "7-8 (Severe)", "9-10 (Extreme)"].index(chief_complaint.get('current_severity'))
            )
        
        with col3:
            chief_complaint['urgency'] = st.selectbox(
                "Urgency Level",
                ["", "Routine", "Urgent", "Emergent", "Crisis"],
                index=0 if not chief_complaint.get('urgency') else ["", "Routine", "Urgent", "Emergent", "Crisis"].index(chief_complaint.get('urgency'))
            )
            
            chief_complaint['previous_episodes'] = st.selectbox(
                "Previous Similar Episodes",
                ["", "First episode", "Second episode", "Multiple episodes", "Chronic/ongoing"],
                index=0 if not chief_complaint.get('previous_episodes') else ["", "First episode", "Second episode", "Multiple episodes", "Chronic/ongoing"].index(chief_complaint.get('previous_episodes'))
            )
        
        # Referral Information
        st.markdown("### 📋 Referral Information")
        col1, col2 = st.columns(2)
        
        with col1:
            chief_complaint['referral_source'] = st.selectbox(
                "Referral Source",
                ["", "Self-referral", "Family member", "Friend", "Primary care physician", "Emergency department", 
                 "Psychiatrist", "Psychologist", "Therapist/counselor", "Social worker", "Court/legal system", 
                 "School counselor", "Employee assistance program", "Insurance company", "Online/telehealth", "Other"],
                index=0 if not chief_complaint.get('referral_source') else ["", "Self-referral", "Family member", "Friend", "Primary care physician", "Emergency department", "Psychiatrist", "Psychologist", "Therapist/counselor", "Social worker", "Court/legal system", "School counselor", "Employee assistance program", "Insurance company", "Online/telehealth", "Other"].index(chief_complaint.get('referral_source'))
            )
            
            chief_complaint['referral_reason'] = st.multiselect(
                "Reason for Referral",
                ["Diagnostic evaluation", "Medication management", "Therapy/counseling", "Second opinion", 
                 "Crisis intervention", "Substance abuse treatment", "Specialized treatment", "Court-ordered evaluation", 
                 "Disability evaluation", "Treatment resistance", "Side effects management", "Suicidal ideation", 
                 "Psychotic symptoms", "Mood stabilization", "Anxiety management"],
                default=chief_complaint.get('referral_reason', [])
            )
        
        with col2:
            chief_complaint['what_helps'] = st.multiselect(
                "What Has Helped Before",
                ["Nothing tried yet", "Medication", "Therapy/counseling", "Support groups", "Exercise", 
                 "Meditation/mindfulness", "Religious/spiritual practices", "Family support", "Time off work", 
                 "Lifestyle changes", "Alternative treatments", "Hospitalization", "Other"],
                default=chief_complaint.get('what_helps', [])
            )
            
            chief_complaint['what_makes_worse'] = st.multiselect(
                "What Makes It Worse",
                ["Stress", "Lack of sleep", "Substance use", "Conflict", "Work pressure", "Financial problems", 
                 "Relationship issues", "Medical problems", "Seasonal changes", "Isolation", "Certain medications", 
                 "Caffeine", "Alcohol", "Other"],
                default=chief_complaint.get('what_makes_worse', [])
            )
        
        # Goals and Expectations
        st.markdown("### 🎯 Treatment Goals and Expectations")
        chief_complaint['treatment_goals'] = st.multiselect(
            "Patient's Treatment Goals",
            ["Feel less depressed", "Reduce anxiety", "Sleep better", "Improve concentration", "Better relationships", 
             "Return to work/school", "Stop substance use", "Manage anger", "Cope with stress", "Medication adjustment", 
             "Understand diagnosis", "Prevent hospitalization", "Improve self-esteem", "Handle trauma", "Other"],
            default=chief_complaint.get('treatment_goals', [])
        )
        
        chief_complaint['expectations'] = st.text_area(
            "Patient's Expectations from Treatment",
            value=chief_complaint.get('expectations', ''),
            height=80,
            help="What does the patient hope to achieve?"
        )
        
        st.session_state.patient_data['chief_complaint'] = chief_complaint

    # Section 2: History of Present Illness
    elif st.session_state.current_section == 2:
        st.markdown('<div class="section-header">History of Present Illness</div>', unsafe_allow_html=True)
        
        if 'history_present_illness' not in st.session_state.patient_data:
            st.session_state.patient_data['history_present_illness'] = {}
        
        hpi = st.session_state.patient_data['history_present_illness']
        
        # Symptom Categories based on DSM-5-TR/ICD-11
        st.markdown("### 🎯 Primary Symptom Categories")
        
        # Mood Disorders
        with st.expander("😔 Mood Disorders", expanded=False):
            if 'mood_symptoms' not in hpi:
                hpi['mood_symptoms'] = {}
            
            st.markdown("**Major Depressive Disorder Symptoms:**")
            hpi['mood_symptoms']['mdd_symptoms'] = st.multiselect(
                "MDD Symptoms (DSM-5-TR)",
                ["Depressed mood", "Anhedonia", "Weight loss/gain", "Insomnia/hypersomnia", 
                 "Psychomotor agitation/retardation", "Fatigue", "Worthlessness/guilt", 
                 "Concentration problems", "Suicidal ideation"],
                default=hpi['mood_symptoms'].get('mdd_symptoms', [])
            )
            
            st.markdown("**Bipolar Disorder Symptoms:**")
            col1, col2 = st.columns(2)
            with col1:
                hpi['mood_symptoms']['manic_symptoms'] = st.multiselect(
                    "Manic/Hypomanic Episodes",
                    ["Elevated mood", "Grandiosity", "Decreased sleep need", "Talkativeness", 
                     "Flight of ideas", "Distractibility", "Increased activity", "Poor judgment"],
                    default=hpi['mood_symptoms'].get('manic_symptoms', [])
                )
            
            with col2:
                hpi['mood_symptoms']['mixed_features'] = st.multiselect(
                    "Mixed Features",
                    ["Depressed mood during mania", "Manic symptoms during depression", 
                     "Rapid cycling", "Irritability"],
                    default=hpi['mood_symptoms'].get('mixed_features', [])
                )
        
        # Anxiety Disorders
        with st.expander("😰 Anxiety Disorders", expanded=False):
            if 'anxiety_symptoms' not in hpi:
                hpi['anxiety_symptoms'] = {}
            
            col1, col2 = st.columns(2)
            with col1:
                hpi['anxiety_symptoms']['gad_symptoms'] = st.multiselect(
                    "Generalized Anxiety Disorder",
                    ["Excessive worry", "Restlessness", "Fatigue", "Concentration problems", 
                     "Irritability", "Muscle tension", "Sleep disturbance"],
                    default=hpi['anxiety_symptoms'].get('gad_symptoms', [])
                )
                
                hpi['anxiety_symptoms']['panic_symptoms'] = st.multiselect(
                    "Panic Disorder",
                    ["Panic attacks", "Palpitations", "Sweating", "Trembling", "Shortness of breath", 
                     "Choking sensation", "Chest pain", "Nausea", "Dizziness", "Fear of dying", 
                     "Fear of losing control", "Derealization"],
                    default=hpi['anxiety_symptoms'].get('panic_symptoms', [])
                )
            
            with col2:
                hpi['anxiety_symptoms']['social_anxiety'] = st.multiselect(
                    "Social Anxiety Disorder",
                    ["Fear of social situations", "Fear of judgment", "Avoidance of social events", 
                     "Physical symptoms in social situations", "Performance anxiety"],
                    default=hpi['anxiety_symptoms'].get('social_anxiety', [])
                )
                
                hpi['anxiety_symptoms']['specific_phobias'] = st.multiselect(
                    "Specific Phobias",
                    ["Animal phobia", "Natural environment", "Blood/injection/injury", 
                     "Situational phobia", "Other specific fears"],
                    default=hpi['anxiety_symptoms'].get('specific_phobias', [])
                )
        
        # Psychotic Disorders
        with st.expander("🌀 Psychotic Disorders", expanded=False):
            if 'psychotic_symptoms' not in hpi:
                hpi['psychotic_symptoms'] = {}
            
            col1, col2 = st.columns(2)
            with col1:
                hpi['psychotic_symptoms']['positive_symptoms'] = st.multiselect(
                    "Positive Symptoms",
                    ["Auditory hallucinations", "Visual hallucinations", "Delusions of persecution", 
                     "Delusions of grandeur", "Delusions of reference", "Thought broadcasting", 
                     "Thought insertion", "Disorganized speech", "Bizarre behavior"],
                    default=hpi['psychotic_symptoms'].get('positive_symptoms', [])
                )
            
            with col2:
                hpi['psychotic_symptoms']['negative_symptoms'] = st.multiselect(
                    "Negative Symptoms",
                    ["Avolition", "Alogia", "Anhedonia", "Affective flattening", 
                     "Social withdrawal", "Poor hygiene", "Lack of motivation"],
                    default=hpi['psychotic_symptoms'].get('negative_symptoms', [])
                )
        
        # OCD and Related Disorders
        with st.expander("🔄 OCD and Related Disorders", expanded=False):
            if 'ocd_symptoms' not in hpi:
                hpi['ocd_symptoms'] = {}
            
            col1, col2 = st.columns(2)
            with col1:
                hpi['ocd_symptoms']['obsessions'] = st.multiselect(
                    "Obsessions",
                    ["Contamination fears", "Symmetry/order", "Aggressive thoughts", 
                     "Sexual thoughts", "Religious/moral concerns", "Somatic concerns"],
                    default=hpi['ocd_symptoms'].get('obsessions', [])
                )
            
            with col2:
                hpi['ocd_symptoms']['compulsions'] = st.multiselect(
                    "Compulsions",
                    ["Washing/cleaning", "Checking", "Ordering/arranging", "Counting", 
                     "Repeating", "Mental rituals", "Reassurance seeking"],
                    default=hpi['ocd_symptoms'].get('compulsions', [])
                )
        
        # Trauma and Stress-Related Disorders
        with st.expander("⚡ Trauma and Stress-Related Disorders", expanded=False):
            if 'trauma_symptoms' not in hpi:
                hpi['trauma_symptoms'] = {}
            
            col1, col2 = st.columns(2)
            with col1:
                hpi['trauma_symptoms']['ptsd_symptoms'] = st.multiselect(
                    "PTSD Symptoms",
                    ["Intrusive memories", "Nightmares", "Flashbacks", "Avoidance of triggers", 
                     "Negative mood changes", "Hypervigilance", "Exaggerated startle", 
                     "Sleep problems", "Concentration problems"],
                    default=hpi['trauma_symptoms'].get('ptsd_symptoms', [])
                )
            
            with col2:
                hpi['trauma_symptoms']['acute_stress'] = st.multiselect(
                    "Acute Stress Symptoms",
                    ["Dissociative symptoms", "Re-experiencing", "Avoidance", 
                     "Negative mood", "Arousal symptoms"],
                    default=hpi['trauma_symptoms'].get('acute_stress', [])
                )
        
        # Eating Disorders
        with st.expander("🍽️ Eating Disorders", expanded=False):
            if 'eating_symptoms' not in hpi:
                hpi['eating_symptoms'] = {}
            
            hpi['eating_symptoms']['eating_disorder_symptoms'] = st.multiselect(
                "Eating Disorder Symptoms",
                ["Restriction of food intake", "Binge eating", "Compensatory behaviors", 
                 "Body image distortion", "Fear of weight gain", "Amenorrhea", 
                 "Preoccupation with food/weight", "Social eating avoidance"],
                default=hpi['eating_symptoms'].get('eating_disorder_symptoms', [])
            )
        
        # Substance Use Disorders
        with st.expander("💊 Substance Use Disorders", expanded=False):
            if 'substance_symptoms' not in hpi:
                hpi['substance_symptoms'] = {}
            
            hpi['substance_symptoms']['sud_symptoms'] = st.multiselect(
                "Substance Use Disorder Symptoms",
                ["Tolerance", "Withdrawal", "Using more than intended", "Unsuccessful quit attempts", 
                 "Time spent obtaining/using", "Social/occupational problems", "Continued use despite problems", 
                 "Craving", "Failure to fulfill obligations", "Hazardous use", "Legal problems"],
                default=hpi['substance_symptoms'].get('sud_symptoms', [])
            )
        
        # Timeline and Course
        st.markdown("### ⏰ Timeline and Course")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            hpi['onset'] = st.selectbox(
                "Onset",
                ["", "Gradual (weeks-months)", "Sudden (days)", "Acute (hours)", "Insidious (years)"],
                index=0 if not hpi.get('onset') else ["", "Gradual (weeks-months)", "Sudden (days)", "Acute (hours)", "Insidious (years)"].index(hpi.get('onset'))
            )
            
            hpi['duration'] = st.selectbox(
                "Duration of Current Episode",
                ["", "Days", "Weeks", "Months", "Years", "Chronic (>2 years)"],
                index=0 if not hpi.get('duration') else ["", "Days", "Weeks", "Months", "Years", "Chronic (>2 years)"].index(hpi.get('duration'))
            )
        
        with col2:
            hpi['course'] = st.selectbox(
                "Course",
                ["", "Stable", "Improving", "Worsening", "Fluctuating", "Episodic"],
                index=0 if not hpi.get('course') else ["", "Stable", "Improving", "Worsening", "Fluctuating", "Episodic"].index(hpi.get('course'))
            )
            
            hpi['severity'] = st.selectbox(
                "Current Severity",
                ["", "Mild", "Moderate", "Severe", "Extreme"],
                index=0 if not hpi.get('severity') else ["", "Mild", "Moderate", "Severe", "Extreme"].index(hpi.get('severity'))
            )
        
        with col3:
            hpi['functional_impairment'] = st.multiselect(
                "Functional Impairment",
                ["Work/school", "Relationships", "Self-care", "Social activities", 
                 "Physical health", "Financial", "Legal"],
                default=hpi.get('functional_impairment', [])
            )
        
        # Precipitating and Contributing Factors
        st.markdown("### 🎯 Contributing Factors")
        col1, col2 = st.columns(2)
        
        with col1:
            hpi['precipitating_factors'] = st.multiselect(
                "Precipitating Factors",
                ["Life stressor", "Medical illness", "Medication change", "Substance use", 
                 "Sleep disruption", "Relationship problems", "Work stress", "Financial stress", 
                 "Loss/grief", "Trauma", "Other"],
                default=hpi.get('precipitating_factors', [])
            )
        
        with col2:
            hpi['aggravating_factors'] = st.multiselect(
                "Aggravating Factors",
                ["Stress", "Lack of sleep", "Substance use", "Isolation", "Conflict", 
                 "Medical problems", "Medication non-compliance", "Seasonal changes"],
                default=hpi.get('aggravating_factors', [])
            )
        
        # Additional narrative
        hpi['additional_details'] = st.text_area(
            "Additional Clinical Details",
            value=hpi.get('additional_details', ''),
            height=100,
            help="Any additional relevant information not captured above"
        )
        
        st.session_state.patient_data['history_present_illness'] = hpi

    # Section 3: Past Psychiatric History
    elif st.session_state.current_section == 3:
        st.markdown('<div class="section-header">Past Psychiatric History</div>', unsafe_allow_html=True)
        
        if 'past_psychiatric_history' not in st.session_state.patient_data:
            st.session_state.patient_data['past_psychiatric_history'] = {}
        
        pph = st.session_state.patient_data['past_psychiatric_history']
        
        # Previous Diagnoses
        st.markdown("### 🏷️ Previous Psychiatric Diagnoses")
        pph['previous_diagnoses'] = st.multiselect(
            "Previous Psychiatric Diagnoses",
            ["Major Depressive Disorder", "Bipolar I Disorder", "Bipolar II Disorder", "Persistent Depressive Disorder", 
             "Generalized Anxiety Disorder", "Panic Disorder", "Social Anxiety Disorder", "Specific Phobia", 
             "Obsessive-Compulsive Disorder", "PTSD", "Acute Stress Disorder", "Adjustment Disorder", 
             "Schizophrenia", "Schizoaffective Disorder", "Brief Psychotic Disorder", "Delusional Disorder", 
             "Substance Use Disorder", "Alcohol Use Disorder", "ADHD", "Autism Spectrum Disorder", 
             "Anorexia Nervosa", "Bulimia Nervosa", "Binge Eating Disorder", "Borderline Personality Disorder", 
             "Antisocial Personality Disorder", "Other Personality Disorder", "Intellectual Disability", 
             "Dementia", "Other"],
            default=pph.get('previous_diagnoses', [])
        )
        
        # Treatment History
        st.markdown("### 🏥 Treatment History")
        col1, col2 = st.columns(2)
        
        with col1:
            pph['hospitalizations'] = st.selectbox(
                "Psychiatric Hospitalizations",
                ["", "None", "1 hospitalization", "2-3 hospitalizations", "4-5 hospitalizations", "More than 5 hospitalizations"],
                index=0 if not pph.get('hospitalizations') else ["", "None", "1 hospitalization", "2-3 hospitalizations", "4-5 hospitalizations", "More than 5 hospitalizations"].index(pph.get('hospitalizations'))
            )
            
            pph['hospitalization_reasons'] = st.multiselect(
                "Reasons for Hospitalization",
                ["Suicidal ideation/attempt", "Homicidal ideation", "Psychosis", "Severe depression", 
                 "Mania/hypomania", "Substance intoxication", "Substance withdrawal", "Self-harm", 
                 "Inability to care for self", "Medication adjustment", "Other"],
                default=pph.get('hospitalization_reasons', [])
            )
            
            pph['emergency_visits'] = st.selectbox(
                "Emergency Department Visits",
                ["", "None", "1-2 visits", "3-5 visits", "More than 5 visits"],
                index=0 if not pph.get('emergency_visits') else ["", "None", "1-2 visits", "3-5 visits", "More than 5 visits"].index(pph.get('emergency_visits'))
            )
        
        with col2:
            pph['outpatient_treatment'] = st.multiselect(
                "Previous Outpatient Treatment",
                ["Individual therapy", "Group therapy", "Family therapy", "Couples therapy", "Medication management", 
                 "Intensive outpatient program", "Partial hospitalization", "Day treatment", "Case management", 
                 "Peer support", "Support groups", "Other"],
                default=pph.get('outpatient_treatment', [])
            )
            
            pph['therapy_types'] = st.multiselect(
                "Types of Therapy Received",
                ["Cognitive Behavioral Therapy (CBT)", "Dialectical Behavior Therapy (DBT)", 
                 "Psychodynamic therapy", "Interpersonal therapy", "EMDR", "Exposure therapy", 
                 "Acceptance and Commitment Therapy", "Mindfulness-based therapy", "Family therapy", 
                 "Group therapy", "Art/music therapy", "Other"],
                default=pph.get('therapy_types', [])
            )
            
            pph['treatment_response'] = st.selectbox(
                "Overall Response to Previous Treatment",
                ["", "Excellent response", "Good response", "Partial response", "Poor response", "No response", "Mixed results"],
                index=0 if not pph.get('treatment_response') else ["", "Excellent response", "Good response", "Partial response", "Poor response", "No response", "Mixed results"].index(pph.get('treatment_response'))
            )
        
        # Medication History
        st.markdown("### 💊 Psychiatric Medication History")
        
        # Antidepressants
        with st.expander("Antidepressants", expanded=False):
            if 'antidepressants_tried' not in pph:
                pph['antidepressants_tried'] = {}
            
            antidepressant_list = ["Sertraline (Zoloft)", "Fluoxetine (Prozac)", "Escitalopram (Lexapro)", 
                                  "Paroxetine (Paxil)", "Citalopram (Celexa)", "Venlafaxine (Effexor)", 
                                  "Duloxetine (Cymbalta)", "Bupropion (Wellbutrin)", "Mirtazapine (Remeron)", 
                                  "Trazodone", "Amitriptyline", "Nortriptyline", "Other"]
            
            pph['antidepressants_tried']['medications'] = st.multiselect(
                "Antidepressants Previously Tried",
                antidepressant_list,
                default=pph['antidepressants_tried'].get('medications', [])
            )
            
            if pph['antidepressants_tried']['medications']:
                pph['antidepressants_tried']['response'] = st.selectbox(
                    "Overall Response to Antidepressants",
                    ["", "Excellent", "Good", "Partial", "Poor", "No response", "Intolerable side effects"],
                    index=0 if not pph['antidepressants_tried'].get('response') else ["", "Excellent", "Good", "Partial", "Poor", "No response", "Intolerable side effects"].index(pph['antidepressants_tried'].get('response'))
                )
        
        # Mood Stabilizers
        with st.expander("Mood Stabilizers", expanded=False):
            if 'mood_stabilizers_tried' not in pph:
                pph['mood_stabilizers_tried'] = {}
            
            pph['mood_stabilizers_tried']['medications'] = st.multiselect(
                "Mood Stabilizers Previously Tried",
                ["Lithium", "Valproic acid (Depakote)", "Carbamazepine (Tegretol)", 
                 "Lamotrigine (Lamictal)", "Oxcarbazepine (Trileptal)", "Topiramate (Topamax)", "Other"],
                default=pph['mood_stabilizers_tried'].get('medications', [])
            )
            
            if pph['mood_stabilizers_tried']['medications']:
                pph['mood_stabilizers_tried']['response'] = st.selectbox(
                    "Response to Mood Stabilizers",
                    ["", "Excellent", "Good", "Partial", "Poor", "No response", "Intolerable side effects"],
                    index=0 if not pph['mood_stabilizers_tried'].get('response') else ["", "Excellent", "Good", "Partial", "Poor", "No response", "Intolerable side effects"].index(pph['mood_stabilizers_tried'].get('response'))
                )
        
        # Antipsychotics
        with st.expander("Antipsychotics", expanded=False):
            if 'antipsychotics_tried' not in pph:
                pph['antipsychotics_tried'] = {}
            
            pph['antipsychotics_tried']['medications'] = st.multiselect(
                "Antipsychotics Previously Tried",
                ["Risperidone (Risperdal)", "Olanzapine (Zyprexa)", "Quetiapine (Seroquel)", 
                 "Aripiprazole (Abilify)", "Ziprasidone (Geodon)", "Paliperidone (Invega)", 
                 "Haloperidol (Haldol)", "Chlorpromazine (Thorazine)", "Clozapine (Clozaril)", "Other"],
                default=pph['antipsychotics_tried'].get('medications', [])
            )
            
            if pph['antipsychotics_tried']['medications']:
                pph['antipsychotics_tried']['response'] = st.selectbox(
                    "Response to Antipsychotics",
                    ["", "Excellent", "Good", "Partial", "Poor", "No response", "Intolerable side effects"],
                    index=0 if not pph['antipsychotics_tried'].get('response') else ["", "Excellent", "Good", "Partial", "Poor", "No response", "Intolerable side effects"].index(pph['antipsychotics_tried'].get('response'))
                )
        
        # Self-harm and Suicide History
        st.markdown("### ⚠️ Self-harm and Suicide History")
        col1, col2 = st.columns(2)
        
        with col1:
            pph['suicide_attempts'] = st.selectbox(
                "Previous Suicide Attempts",
                ["", "None", "1 attempt", "2-3 attempts", "4-5 attempts", "More than 5 attempts"],
                index=0 if not pph.get('suicide_attempts') else ["", "None", "1 attempt", "2-3 attempts", "4-5 attempts", "More than 5 attempts"].index(pph.get('suicide_attempts'))
            )
            
            if pph.get('suicide_attempts') and pph['suicide_attempts'] != "None":
                pph['suicide_methods'] = st.multiselect(
                    "Methods Used in Attempts",
                    ["Overdose", "Cutting", "Hanging", "Jumping", "Firearms", "Other"],
                    default=pph.get('suicide_methods', [])
                )
        
        with col2:
            pph['self_harm_history'] = st.selectbox(
                "Self-harm History",
                ["", "None", "Past only", "Recent (within 1 year)", "Current"],
                index=0 if not pph.get('self_harm_history') else ["", "None", "Past only", "Recent (within 1 year)", "Current"].index(pph.get('self_harm_history'))
            )
            
            if pph.get('self_harm_history') and pph['self_harm_history'] != "None":
                pph['self_harm_methods'] = st.multiselect(
                    "Self-harm Methods",
                    ["Cutting", "Burning", "Hitting", "Hair pulling", "Scratching", "Other"],
                    default=pph.get('self_harm_methods', [])
                )
        
        st.session_state.patient_data['past_psychiatric_history'] = pph

    # Section 4: Past Medical History
    elif st.session_state.current_section == 4:
        st.markdown('<div class="section-header">Past Medical History</div>', unsafe_allow_html=True)
        
        if 'past_medical_history' not in st.session_state.patient_data:
            st.session_state.patient_data['past_medical_history'] = {}
        
        pmh = st.session_state.patient_data['past_medical_history']
        
        # Medical Conditions by System
        st.markdown("### 🏥 Medical Conditions by System")
        
        if 'medical_conditions' not in pmh:
            pmh['medical_conditions'] = {}
        
        # Cardiovascular
        with st.expander("❤️ Cardiovascular", expanded=False):
            pmh['medical_conditions']['cardiovascular'] = st.multiselect(
                "Cardiovascular Conditions",
                ["Hypertension", "Coronary artery disease", "Heart failure", "Arrhythmias", 
                 "Valvular disease", "Peripheral vascular disease", "Deep vein thrombosis", 
                 "Pulmonary embolism", "Stroke", "TIA"],
                default=pmh['medical_conditions'].get('cardiovascular', [])
            )
        
        # Endocrine
        with st.expander("🦋 Endocrine", expanded=False):
            pmh['medical_conditions']['endocrine'] = st.multiselect(
                "Endocrine Conditions",
                ["Diabetes mellitus Type 1", "Diabetes mellitus Type 2", "Thyroid disorders", 
                 "Hyperthyroidism", "Hypothyroidism", "Adrenal disorders", "Pituitary disorders", 
                 "Metabolic syndrome", "Obesity"],
                default=pmh['medical_conditions'].get('endocrine', [])
            )
        
        # Neurological
        with st.expander("🧠 Neurological", expanded=False):
            pmh['medical_conditions']['neurological'] = st.multiselect(
                "Neurological Conditions",
                ["Seizure disorder", "Epilepsy", "Migraine", "Tension headaches", "Multiple sclerosis", 
                 "Parkinson's disease", "Dementia", "Traumatic brain injury", "Neuropathy", 
                 "Movement disorders"],
                default=pmh['medical_conditions'].get('neurological', [])
            )
        
        # Respiratory
        with st.expander("🫁 Respiratory", expanded=False):
            pmh['medical_conditions']['respiratory'] = st.multiselect(
                "Respiratory Conditions",
                ["Asthma", "COPD", "Sleep apnea", "Pneumonia", "Tuberculosis", 
                 "Pulmonary fibrosis", "Lung cancer", "Chronic bronchitis"],
                default=pmh['medical_conditions'].get('respiratory', [])
            )
        
        # Gastrointestinal
        with st.expander("🍽️ Gastrointestinal", expanded=False):
            pmh['medical_conditions']['gastrointestinal'] = st.multiselect(
                "Gastrointestinal Conditions",
                ["GERD", "Peptic ulcer disease", "IBD", "IBS", "Hepatitis", "Cirrhosis", 
                 "Gallbladder disease", "Pancreatitis", "Colon cancer"],
                default=pmh['medical_conditions'].get('gastrointestinal', [])
            )
        
        # Current Medications by Category
        st.markdown("### 💊 Current Medications")
        
        if 'medications' not in pmh:
            pmh['medications'] = {}
        
        # Psychiatric Medications
        with st.expander("🧠 Psychiatric Medications", expanded=True):
            col1, col2 = st.columns(2)
            
            with col1:
                pmh['medications']['antidepressants'] = st.multiselect(
                    "Antidepressants",
                    ["Sertraline (Zoloft)", "Fluoxetine (Prozac)", "Escitalopram (Lexapro)", 
                     "Paroxetine (Paxil)", "Citalopram (Celexa)", "Venlafaxine (Effexor)", 
                     "Duloxetine (Cymbalta)", "Bupropion (Wellbutrin)", "Mirtazapine (Remeron)", 
                     "Trazodone", "Amitriptyline", "Nortriptyline", "Other"],
                    default=pmh['medications'].get('antidepressants', [])
                )
                
                pmh['medications']['mood_stabilizers'] = st.multiselect(
                    "Mood Stabilizers",
                    ["Lithium", "Valproic acid (Depakote)", "Carbamazepine (Tegretol)", 
                     "Lamotrigine (Lamictal)", "Oxcarbazepine (Trileptal)", "Other"],
                    default=pmh['medications'].get('mood_stabilizers', [])
                )
            
            with col2:
                pmh['medications']['antipsychotics'] = st.multiselect(
                    "Antipsychotics",
                    ["Risperidone (Risperdal)", "Olanzapine (Zyprexa)", "Quetiapine (Seroquel)", 
                     "Aripiprazole (Abilify)", "Ziprasidone (Geodon)", "Paliperidone (Invega)", 
                     "Haloperidol (Haldol)", "Chlorpromazine (Thorazine)", "Clozapine (Clozaril)", "Other"],
                    default=pmh['medications'].get('antipsychotics', [])
                )
                
                pmh['medications']['anxiolytics'] = st.multiselect(
                    "Anxiolytics/Sedatives",
                    ["Lorazepam (Ativan)", "Alprazolam (Xanax)", "Clonazepam (Klonopin)", 
                     "Diazepam (Valium)", "Temazepam (Restoril)", "Zolpidem (Ambien)", 
                     "Eszopiclone (Lunesta)", "Hydroxyzine (Vistaril)", "Other"],
                    default=pmh['medications'].get('anxiolytics', [])
                )
        
        # Medical Medications
        with st.expander("🏥 Medical Medications", expanded=False):
            col1, col2 = st.columns(2)
            
            with col1:
                pmh['medications']['cardiovascular_meds'] = st.multiselect(
                    "Cardiovascular Medications",
                    ["ACE inhibitors", "ARBs", "Beta blockers", "Calcium channel blockers", 
                     "Diuretics", "Statins", "Anticoagulants", "Antiplatelets", "Other"],
                    default=pmh['medications'].get('cardiovascular_meds', [])
                )
                
                pmh['medications']['diabetes_meds'] = st.multiselect(
                    "Diabetes Medications",
                    ["Metformin", "Insulin", "Sulfonylureas", "DPP-4 inhibitors", 
                     "GLP-1 agonists", "SGLT-2 inhibitors", "Other"],
                    default=pmh['medications'].get('diabetes_meds', [])
                )
            
            with col2:
                pmh['medications']['pain_meds'] = st.multiselect(
                    "Pain Medications",
                    ["Acetaminophen", "NSAIDs", "Opioids", "Muscle relaxants", 
                     "Anticonvulsants for pain", "Topical analgesics", "Other"],
                    default=pmh['medications'].get('pain_meds', [])
                )
                
                pmh['medications']['other_meds'] = st.text_area(
                    "Other Medications",
                    value=pmh['medications'].get('other_meds', ''),
                    height=80,
                    help="List any other medications not covered above"
                )
        
        # Allergies and Adverse Reactions
        st.markdown("### ⚠️ Allergies and Adverse Reactions")
        col1, col2 = st.columns(2)
        
        with col1:
            if 'allergies' not in pmh:
                pmh['allergies'] = {}
            
            pmh['allergies']['drug_allergies'] = st.multiselect(
                "Drug Allergies",
                ["Penicillin", "Sulfa drugs", "NSAIDs", "Codeine", "Morphine", 
                 "Contrast dye", "Latex", "Other"],
                default=pmh['allergies'].get('drug_allergies', [])
            )
            
            pmh['allergies']['environmental_allergies'] = st.multiselect(
                "Environmental Allergies",
                ["Pollen", "Dust mites", "Pet dander", "Mold", "Food allergies", "Other"],
                default=pmh['allergies'].get('environmental_allergies', [])
            )
        
        with col2:
            pmh['allergies']['reaction_details'] = st.text_area(
                "Allergy Reaction Details",
                value=pmh['allergies'].get('reaction_details', ''),
                height=100,
                help="Describe specific reactions (rash, anaphylaxis, etc.)"
            )
        
        # Surgical History
        st.markdown("### 🔪 Surgical History")
        if 'surgeries' not in pmh:
            pmh['surgeries'] = {}
        
        pmh['surgeries']['surgical_procedures'] = st.multiselect(
            "Previous Surgeries",
            ["Appendectomy", "Cholecystectomy", "Hernia repair", "Joint replacement", 
             "Cardiac surgery", "Neurosurgery", "Gynecological surgery", "Urological surgery", 
             "Gastrointestinal surgery", "Other"],
            default=pmh['surgeries'].get('surgical_procedures', [])
        )
        
        if pmh['surgeries']['surgical_procedures']:
            pmh['surgeries']['surgery_details'] = st.text_area(
                "Surgery Details and Dates",
                value=pmh['surgeries'].get('surgery_details', ''),
                height=80,
                help="Provide dates and any complications"
            )
        
        st.session_state.patient_data['past_medical_history'] = pmh

    # Section 5: Family History
    elif st.session_state.current_section == 5:
        st.markdown('<div class="section-header">Family History</div>', unsafe_allow_html=True)
        
        if 'family_history' not in st.session_state.patient_data:
            st.session_state.patient_data['family_history'] = {}
        
        fh = st.session_state.patient_data['family_history']
        
        col1, col2 = st.columns(2)
        with col1:
            fh['psychiatric_history'] = st.multiselect(
                "Family Psychiatric History",
                ["Depression", "Anxiety", "Bipolar Disorder", "Schizophrenia", "Substance Use", "Suicide", "Other", "None known"],
                default=fh.get('psychiatric_history', [])
            )
            
            fh['medical_history'] = st.text_area(
                "Family Medical History",
                value=fh.get('medical_history', ''),
                height=100
            )
        
        with col2:
            fh['family_structure'] = st.text_area(
                "Family Structure",
                value=fh.get('family_structure', ''),
                height=100,
                help="Describe family composition and relationships"
            )
            
            fh['family_support'] = st.selectbox(
                "Family Support Level",
                ["Strong", "Moderate", "Limited", "None"],
                index=0 if not fh.get('family_support') else ["Strong", "Moderate", "Limited", "None"].index(fh.get('family_support'))
            )
        
        st.session_state.patient_data['family_history'] = fh

    # Section 6: Social & Developmental History
    elif st.session_state.current_section == 6:
        st.markdown('<div class="section-header">Social & Developmental History</div>', unsafe_allow_html=True)
        
        if 'social_developmental_history' not in st.session_state.patient_data:
            st.session_state.patient_data['social_developmental_history'] = {}
        
        sdh = st.session_state.patient_data['social_developmental_history']
        
        # Childhood and Development
        st.markdown("### 👶 Childhood and Early Development")
        col1, col2 = st.columns(2)
        
        with col1:
            if 'childhood' not in sdh:
                sdh['childhood'] = {}
            
            sdh['childhood']['birth_complications'] = st.multiselect(
                "Birth/Pregnancy Complications",
                ["None known", "Premature birth", "Low birth weight", "Cesarean delivery", 
                 "Complications during pregnancy", "Complications during delivery", "NICU stay", "Other"],
                default=sdh['childhood'].get('birth_complications', [])
            )
            
            sdh['childhood']['developmental_milestones'] = st.selectbox(
                "Developmental Milestones",
                ["", "Normal/on time", "Some delays", "Significant delays", "Unknown"],
                index=0 if not sdh['childhood'].get('developmental_milestones') else ["", "Normal/on time", "Some delays", "Significant delays", "Unknown"].index(sdh['childhood'].get('developmental_milestones'))
            )
            
            sdh['childhood']['childhood_illnesses'] = st.multiselect(
                "Significant Childhood Illnesses",
                ["None", "Frequent infections", "Asthma", "Seizures", "Head injury", 
                 "Chronic medical condition", "Hospitalizations", "Other"],
                default=sdh['childhood'].get('childhood_illnesses', [])
            )
        
        with col2:
            sdh['childhood']['school_performance'] = st.selectbox(
                "School Performance",
                ["", "Excellent", "Good", "Average", "Below average", "Poor", "Special education"],
                index=0 if not sdh['childhood'].get('school_performance') else ["", "Excellent", "Good", "Average", "Below average", "Poor", "Special education"].index(sdh['childhood'].get('school_performance'))
            )
            
            sdh['childhood']['behavioral_problems'] = st.multiselect(
                "Childhood Behavioral Problems",
                ["None", "ADHD symptoms", "Oppositional behavior", "Aggression", "Social difficulties", 
                 "Learning disabilities", "Anxiety", "Depression", "Autism spectrum symptoms", "Other"],
                default=sdh['childhood'].get('behavioral_problems', [])
            )
            
            sdh['childhood']['family_structure'] = st.selectbox(
                "Childhood Family Structure",
                ["", "Two-parent household", "Single parent", "Divorced parents", "Blended family", 
                 "Raised by grandparents", "Foster care", "Adoption", "Other relatives", "Other"],
                index=0 if not sdh['childhood'].get('family_structure') else ["", "Two-parent household", "Single parent", "Divorced parents", "Blended family", "Raised by grandparents", "Foster care", "Adoption", "Other relatives", "Other"].index(sdh['childhood'].get('family_structure'))
            )
        
        # Trauma and Adverse Experiences
        st.markdown("### ⚠️ Trauma and Adverse Childhood Experiences")
        if 'trauma' not in sdh:
            sdh['trauma'] = {}
        
        sdh['trauma']['childhood_trauma'] = st.multiselect(
            "Childhood Trauma/Adverse Experiences",
            ["None", "Physical abuse", "Sexual abuse", "Emotional abuse", "Neglect", 
             "Domestic violence witnessed", "Parent with mental illness", "Parent with substance abuse", 
             "Incarcerated family member", "Death of parent/caregiver", "Serious family illness", 
             "Poverty", "Homelessness", "Bullying", "Other"],
            default=sdh['trauma'].get('childhood_trauma', [])
        )
        
        sdh['trauma']['adult_trauma'] = st.multiselect(
            "Adult Trauma Experiences",
            ["None", "Physical assault", "Sexual assault", "Domestic violence", "Combat exposure", 
             "Motor vehicle accident", "Natural disaster", "Serious illness/injury", "Death of loved one", 
             "Workplace violence", "Robbery/mugging", "Other violent crime", "Medical trauma", "Other"],
            default=sdh['trauma'].get('adult_trauma', [])
        )
        
        if sdh['trauma']['childhood_trauma'] != ["None"] or sdh['trauma']['adult_trauma'] != ["None"]:
            sdh['trauma']['trauma_treatment'] = st.multiselect(
                "Trauma Treatment Received",
                ["None", "Individual therapy", "Group therapy", "EMDR", "Cognitive processing therapy", 
                 "Prolonged exposure therapy", "Medication", "Support groups", "Other"],
                default=sdh['trauma'].get('trauma_treatment', [])
            )
        
        # Social Relationships
        st.markdown("### 👥 Social Relationships and Support")
        col1, col2 = st.columns(2)
        
        with col1:
            if 'relationships' not in sdh:
                sdh['relationships'] = {}
            
            sdh['relationships']['marital_status'] = st.selectbox(
                "Current Relationship Status",
                ["", "Single", "Dating", "Married", "Divorced", "Separated", "Widowed", "Cohabiting"],
                index=0 if not sdh['relationships'].get('marital_status') else ["", "Single", "Dating", "Married", "Divorced", "Separated", "Widowed", "Cohabiting"].index(sdh['relationships'].get('marital_status'))
            )
            
            sdh['relationships']['relationship_quality'] = st.selectbox(
                "Current Relationship Quality",
                ["", "Excellent", "Good", "Fair", "Poor", "Conflicted", "Not applicable"],
                index=0 if not sdh['relationships'].get('relationship_quality') else ["", "Excellent", "Good", "Fair", "Poor", "Conflicted", "Not applicable"].index(sdh['relationships'].get('relationship_quality'))
            )
            
            sdh['relationships']['social_support'] = st.selectbox(
                "Social Support Level",
                ["", "Strong support network", "Moderate support", "Limited support", "Isolated", "No support"],
                index=0 if not sdh['relationships'].get('social_support') else ["", "Strong support network", "Moderate support", "Limited support", "Isolated", "No support"].index(sdh['relationships'].get('social_support'))
            )
        
        with col2:
            sdh['relationships']['children'] = st.selectbox(
                "Children",
                ["", "None", "1 child", "2 children", "3 children", "4+ children"],
                index=0 if not sdh['relationships'].get('children') else ["", "None", "1 child", "2 children", "3 children", "4+ children"].index(sdh['relationships'].get('children'))
            )
            
            sdh['relationships']['relationship_problems'] = st.multiselect(
                "Relationship Problems",
                ["None", "Communication issues", "Trust issues", "Intimacy problems", "Financial disagreements", 
                 "Parenting disagreements", "In-law problems", "Jealousy", "Domestic violence", "Infidelity", "Other"],
                default=sdh['relationships'].get('relationship_problems', [])
            )
            
            sdh['relationships']['social_activities'] = st.multiselect(
                "Social Activities",
                ["None", "Family gatherings", "Friends", "Religious activities", "Community groups", 
                 "Sports/recreation", "Hobbies", "Volunteering", "Online communities", "Other"],
                default=sdh['relationships'].get('social_activities', [])
            )
        
        # Legal History
        st.markdown("### ⚖️ Legal History")
        if 'legal' not in sdh:
            sdh['legal'] = {}
        
        col1, col2 = st.columns(2)
        
        with col1:
            sdh['legal']['legal_problems'] = st.multiselect(
                "Legal Problems",
                ["None", "DUI/DWI", "Drug charges", "Theft", "Assault", "Domestic violence", 
                 "Traffic violations", "Probation violations", "Civil lawsuits", "Bankruptcy", "Other"],
                default=sdh['legal'].get('legal_problems', [])
            )
            
            sdh['legal']['incarceration_history'] = st.selectbox(
                "Incarceration History",
                ["", "None", "Jail (days)", "Jail (weeks)", "Prison (months)", "Prison (years)", "Multiple times"],
                index=0 if not sdh['legal'].get('incarceration_history') else ["", "None", "Jail (days)", "Jail (weeks)", "Prison (months)", "Prison (years)", "Multiple times"].index(sdh['legal'].get('incarceration_history'))
            )
        
        with col2:
            sdh['legal']['current_legal_status'] = st.selectbox(
                "Current Legal Status",
                ["", "No legal issues", "Pending charges", "On probation", "On parole", "Court-ordered treatment", "Other"],
                index=0 if not sdh['legal'].get('current_legal_status') else ["", "No legal issues", "Pending charges", "On probation", "On parole", "Court-ordered treatment", "Other"].index(sdh['legal'].get('current_legal_status'))
            )
            
            sdh['legal']['legal_consequences'] = st.multiselect(
                "Legal Consequences of Mental Health/Substance Use",
                ["None", "Arrests", "Fines", "License suspension", "Job loss", "Custody issues", 
                 "Restraining orders", "Mandated treatment", "Other"],
                default=sdh['legal'].get('legal_consequences', [])
            )
        
        # Cultural and Religious Background
        st.markdown("### 🌍 Cultural and Religious Background")
        if 'cultural' not in sdh:
            sdh['cultural'] = {}
        
        col1, col2 = st.columns(2)
        
        with col1:
            sdh['cultural']['cultural_background'] = st.text_input(
                "Cultural/Ethnic Background",
                value=sdh['cultural'].get('cultural_background', ''),
                help="Patient's cultural or ethnic identity"
            )
            
            sdh['cultural']['primary_language'] = st.selectbox(
                "Primary Language",
                ["", "English", "Spanish", "Mandarin", "Arabic", "French", "German", "Other"],
                index=0 if not sdh['cultural'].get('primary_language') else ["", "English", "Spanish", "Mandarin", "Arabic", "French", "German", "Other"].index(sdh['cultural'].get('primary_language'))
            )
        
        with col2:
            sdh['cultural']['religious_spiritual'] = st.selectbox(
                "Religious/Spiritual Beliefs",
                ["", "Christian", "Catholic", "Jewish", "Muslim", "Hindu", "Buddhist", "Atheist", "Agnostic", "Spiritual but not religious", "Other"],
                index=0 if not sdh['cultural'].get('religious_spiritual') else ["", "Christian", "Catholic", "Jewish", "Muslim", "Hindu", "Buddhist", "Atheist", "Agnostic", "Spiritual but not religious", "Other"].index(sdh['cultural'].get('religious_spiritual'))
            )
            
            sdh['cultural']['cultural_factors'] = st.multiselect(
                "Cultural Factors Affecting Treatment",
                ["None", "Language barriers", "Religious beliefs", "Cultural stigma", "Family expectations", 
                 "Traditional healing practices", "Gender roles", "Immigration status", "Other"],
                default=sdh['cultural'].get('cultural_factors', [])
            )
        
        st.session_state.patient_data['social_developmental_history'] = sdh

    # Section 7: Substance Use Assessment (with conditional logic and fixed session state)
    elif st.session_state.current_section == 7:
        st.markdown('<div class="section-header">Comprehensive Substance Use Assessment</div>', unsafe_allow_html=True)
        
        if 'substance_use' not in st.session_state.patient_data:
            st.session_state.patient_data['substance_use'] = {}
        
        substance_data = st.session_state.patient_data['substance_use']
        
        # Quick Screening
        st.markdown("### 🔍 Quick Screening")
        col1, col2 = st.columns(2)
        with col1:
            substance_data['any_substance_use'] = st.selectbox(
                "Any Substance Use (including alcohol)",
                ["No", "Yes - past only", "Yes - current"],
                index=0 if not substance_data.get('any_substance_use') else ["No", "Yes - past only", "Yes - current"].index(substance_data.get('any_substance_use'))
            )
        
        with col2:
            substance_data['substance_related_problems'] = st.selectbox(
                "Substance-Related Problems",
                ["None", "Mild", "Moderate", "Severe"],
                index=0 if not substance_data.get('substance_related_problems') else ["None", "Mild", "Moderate", "Severe"].index(substance_data.get('substance_related_problems'))
            )
        
        # Alcohol Assessment
        st.markdown("### 🍷 Alcohol Use")
        if 'alcohol' not in substance_data:
            substance_data['alcohol'] = {}
        
        col1, col2, col3 = st.columns(3)
        with col1:
            substance_data['alcohol']['current_use'] = st.selectbox(
                "Current Alcohol Use",
                ["Never used", "Abstinent (>1 year)", "Abstinent (<1 year)", "Occasional social", "Regular use", "Heavy use"],
                index=0 if not substance_data['alcohol'].get('current_use') else ["Never used", "Abstinent (>1 year)", "Abstinent (<1 year)", "Occasional social", "Regular use", "Heavy use"].index(substance_data['alcohol'].get('current_use'))
            )
            
            substance_data['alcohol']['age_onset'] = st.number_input(
                "Age First Used Alcohol",
                min_value=0, max_value=50,
                value=substance_data['alcohol'].get('age_onset') if substance_data['alcohol'].get('age_onset') else None
            )
        
        with col2:
            if substance_data['alcohol']['current_use'] not in ["Never used", "Abstinent (>1 year)", "Abstinent (<1 year)"]:
                substance_data['alcohol']['drinks_per_week'] = st.number_input(
                    "Drinks per Week",
                    min_value=0, max_value=100,
                    value=substance_data['alcohol'].get('drinks_per_week') if substance_data['alcohol'].get('drinks_per_week') else None
                )
                
                substance_data['alcohol']['binge_episodes'] = st.selectbox(
                    "Binge Drinking (5+ drinks)",
                    ["Never", "Monthly", "Weekly", "Multiple times/week", "Daily"],
                    index=0 if not substance_data['alcohol'].get('binge_episodes') else ["Never", "Monthly", "Weekly", "Multiple times/week", "Daily"].index(substance_data['alcohol'].get('binge_episodes'))
                )
            else:
                substance_data['alcohol']['drinks_per_week'] = 0
                substance_data['alcohol']['binge_episodes'] = "Never"
        
        with col3:
            substance_data['alcohol']['last_drink'] = st.selectbox(
                "Last Drink",
                ["Never", "Today", "Yesterday", "This week", "This month", "Months ago", "Years ago"],
                index=0 if not substance_data['alcohol'].get('last_drink') else ["Never", "Today", "Yesterday", "This week", "This month", "Months ago", "Years ago"].index(substance_data['alcohol'].get('last_drink'))
            )
            
            substance_data['alcohol']['treatment_history'] = st.selectbox(
                "Alcohol Treatment",
                ["None", "AA/NA meetings", "Outpatient", "Inpatient", "Multiple treatments"],
                index=0 if not substance_data['alcohol'].get('treatment_history') else ["None", "AA/NA meetings", "Outpatient", "Inpatient", "Multiple treatments"].index(substance_data['alcohol'].get('treatment_history'))
            )
        
        # Alcohol Problems Checklist
        if substance_data['alcohol']['current_use'] not in ["Never used"]:
            substance_data['alcohol']['problems'] = st.multiselect(
                "Alcohol-Related Problems",
                ["Legal issues (DUI, arrests)", "Work/school problems", "Relationship problems", "Financial problems", 
                 "Health problems", "Blackouts", "Withdrawal symptoms", "Tolerance", "Failed quit attempts"],
                default=substance_data['alcohol'].get('problems', [])
            )
        
        # Drug Use Assessment
        st.markdown("### 💊 Drug Use")
        if 'drugs' not in substance_data:
            substance_data['drugs'] = {}
        
        # Quick drug screening
        col1, col2 = st.columns(2)
        with col1:
            substance_data['drugs']['any_drug_use'] = st.selectbox(
                "Any Illicit Drug Use",
                ["Never", "Past only", "Current"],
                index=0 if not substance_data['drugs'].get('any_drug_use') else ["Never", "Past only", "Current"].index(substance_data['drugs'].get('any_drug_use'))
            )
        
        with col2:
            substance_data['drugs']['prescription_misuse'] = st.selectbox(
                "Prescription Drug Misuse",
                ["Never", "Past only", "Current"],
                index=0 if not substance_data['drugs'].get('prescription_misuse') else ["Never", "Past only", "Current"].index(substance_data['drugs'].get('prescription_misuse'))
            )
        
        # Specific Substances
        if substance_data['drugs']['any_drug_use'] != "Never" or substance_data['drugs']['prescription_misuse'] != "Never":
            st.markdown("#### Specific Substances Used")
            
            substance_categories = {
                "Cannabis": ["Marijuana", "Hash", "Edibles", "Concentrates"],
                "Stimulants": ["Cocaine", "Crack cocaine", "Methamphetamine", "Amphetamines", "ADHD medications"],
                "Opioids": ["Heroin", "Prescription opioids", "Fentanyl", "Oxycodone", "Hydrocodone"],
                "Depressants": ["Benzodiazepines", "Barbiturates", "Sleep medications"],
                "Hallucinogens": ["LSD", "Mushrooms", "PCP", "MDMA/Ecstasy", "Ketamine"],
                "Other": ["Inhalants", "Synthetic drugs", "Over-the-counter medications"]
            }
            
            substance_data['drugs']['substances_used'] = []
            for category, substances in substance_categories.items():
                with st.expander(f"{category}"):
                    selected = st.multiselect(
                        f"Select {category} used:",
                        substances,
                        default=[s for s in substances if s in substance_data['drugs'].get('substances_used', [])]
                    )
                    substance_data['drugs']['substances_used'].extend(selected)
            
            # Remove duplicates
            substance_data['drugs']['substances_used'] = list(set(substance_data['drugs']['substances_used']))
            
            # High-risk behaviors
            st.markdown("#### High-Risk Behaviors")
            col1, col2, col3 = st.columns(3)
            
            with col1:
                substance_data['drugs']['injection_history'] = st.selectbox(
                    "Injection Drug Use",
                    ["Never", "Past only", "Current"],
                    index=0 if not substance_data['drugs'].get('injection_history') else ["Never", "Past only", "Current"].index(substance_data['drugs'].get('injection_history'))
                )
            
            with col2:
                substance_data['drugs']['needle_sharing'] = st.selectbox(
                    "Needle Sharing",
                    ["Never", "Past only", "Current"],
                    index=0 if not substance_data['drugs'].get('needle_sharing') else ["Never", "Past only", "Current"].index(substance_data['drugs'].get('needle_sharing'))
                )
            
            with col3:
                substance_data['drugs']['overdose_history'] = st.selectbox(
                    "Overdose History",
                    ["Never", "1 time", "2-3 times", "4+ times"],
                    index=0 if not substance_data['drugs'].get('overdose_history') else ["Never", "1 time", "2-3 times", "4+ times"].index(substance_data['drugs'].get('overdose_history'))
                )
        
        # Tobacco/Nicotine
        st.markdown("### 🚬 Tobacco/Nicotine Use")
        if 'tobacco' not in substance_data:
            substance_data['tobacco'] = {}
        
        col1, col2, col3 = st.columns(3)
        with col1:
            substance_data['tobacco']['current_use'] = st.selectbox(
                "Current Tobacco Use",
                ["Never used", "Former user", "Current user"],
                index=0 if not substance_data['tobacco'].get('current_use') else ["Never used", "Former user", "Current user"].index(substance_data['tobacco'].get('current_use'))
            )
        
        with col2:
            if substance_data['tobacco']['current_use'] == "Current user":
                substance_data['tobacco']['cigarettes_per_day'] = st.number_input(
                    "Cigarettes per Day",
                    min_value=0, max_value=60,
                    value=substance_data['tobacco'].get('cigarettes_per_day', 0)
                )
            else:
                substance_data['tobacco']['cigarettes_per_day'] = 0
        
        with col3:
            substance_data['tobacco']['quit_attempts'] = st.selectbox(
                "Quit Attempts",
                ["None", "1-2 attempts", "3-5 attempts", "Many attempts"],
                index=0 if not substance_data['tobacco'].get('quit_attempts') else ["None", "1-2 attempts", "3-5 attempts", "Many attempts"].index(substance_data['tobacco'].get('quit_attempts'))
            )
        
        # Caffeine
        st.markdown("### ☕ Caffeine Use")
        if 'caffeine' not in substance_data:
            substance_data['caffeine'] = {}
        
        col1, col2 = st.columns(2)
        with col1:
            substance_data['caffeine']['daily_intake'] = st.selectbox(
                "Daily Caffeine Intake",
                ["None", "1-2 cups coffee/day", "3-5 cups coffee/day", "6+ cups coffee/day", "Energy drinks", "Excessive (>400mg/day)"],
                index=0 if not substance_data['caffeine'].get('daily_intake') else ["None", "1-2 cups coffee/day", "3-5 cups coffee/day", "6+ cups coffee/day", "Energy drinks", "Excessive (>400mg/day)"].index(substance_data['caffeine'].get('daily_intake'))
            )
        
        with col2:
            substance_data['caffeine']['withdrawal_symptoms'] = st.multiselect(
                "Caffeine Withdrawal Symptoms",
                ["None", "Headaches", "Fatigue", "Irritability", "Difficulty concentrating"],
                default=substance_data['caffeine'].get('withdrawal_symptoms', [])
            )
        
        st.session_state.patient_data['substance_use'] = substance_data

    # Section 8: Mental State Examination
    elif st.session_state.current_section == 8:
        st.markdown('<div class="section-header">Mental State Examination</div>', unsafe_allow_html=True)
        
        if 'mental_state_examination' not in st.session_state.patient_data:
            st.session_state.patient_data['mental_state_examination'] = {}
        
        mse = st.session_state.patient_data['mental_state_examination']
        
        # Appearance & Behavior
        st.markdown("### 👤 Appearance & Behavior")
        col1, col2 = st.columns(2)
        with col1:
            mse['appearance'] = st.multiselect(
                "Appearance",
                ['Well-groomed', 'Unkempt', 'Bizarre dress', 'Age-appropriate', 'Malnourished', 'Obese', 'Poor hygiene', 'Inappropriate dress'],
                default=mse.get('appearance', [])
            )
            
            mse['eye_contact'] = st.selectbox(
                "Eye Contact",
                ['Appropriate', 'Poor', 'Excessive', 'Avoidant'],
                index=0 if not mse.get('eye_contact') else ['Appropriate', 'Poor', 'Excessive', 'Avoidant'].index(mse.get('eye_contact'))
            )
        
        with col2:
            mse['behavior'] = st.multiselect(
                "Behavior",
                ['Cooperative', 'Agitated', 'Withdrawn', 'Hostile', 'Psychomotor retardation', 'Psychomotor agitation', 'Catatonic', 'Restless', 'Guarded'],
                default=mse.get('behavior', [])
            )
            
            mse['attitude'] = st.selectbox(
                "Attitude Toward Examiner",
                ['Cooperative', 'Uncooperative', 'Hostile', 'Suspicious', 'Seductive', 'Demanding'],
                index=0 if not mse.get('attitude') else ['Cooperative', 'Uncooperative', 'Hostile', 'Suspicious', 'Seductive', 'Demanding'].index(mse.get('attitude'))
            )
        
        # Speech & Language
        st.markdown("### 🗣️ Speech & Language")
        col1, col2, col3 = st.columns(3)
        with col1:
            mse['speech_rate'] = st.selectbox(
                "Speech Rate",
                ['Normal', 'Slow', 'Rapid', 'Pressured'],
                index=0 if not mse.get('speech_rate') else ['Normal', 'Slow', 'Rapid', 'Pressured'].index(mse.get('speech_rate'))
            )
        
        with col2:
            mse['speech_volume'] = st.selectbox(
                "Speech Volume",
                ['Normal', 'Loud', 'Soft', 'Whispered'],
                index=0 if not mse.get('speech_volume') else ['Normal', 'Loud', 'Soft', 'Whispered'].index(mse.get('speech_volume'))
            )
        
        with col3:
            mse['speech_quality'] = st.multiselect(
                "Speech Quality",
                ['Clear', 'Slurred', 'Stuttering', 'Monotone', 'Dysarthric', 'Aphasia'],
                default=mse.get('speech_quality', [])
            )
        
        # Mood & Affect
        st.markdown("### 😊 Mood & Affect")
        col1, col2 = st.columns(2)
        with col1:
            mse['mood'] = st.selectbox(
                "Mood (Subjective)",
                ['Euthymic', 'Depressed', 'Elevated', 'Irritable', 'Anxious', 'Angry', 'Euphoric', 'Dysthymic', 'Mixed'],
                index=0 if not mse.get('mood') else ['Euthymic', 'Depressed', 'Elevated', 'Irritable', 'Anxious', 'Angry', 'Euphoric', 'Dysthymic', 'Mixed'].index(mse.get('mood'))
            )
            
            mse['mood_congruence'] = st.selectbox(
                "Mood Congruence",
                ['Congruent with affect', 'Incongruent with affect'],
                index=0 if not mse.get('mood_congruence') else ['Congruent with affect', 'Incongruent with affect'].index(mse.get('mood_congruence'))
            )
        
        with col2:
            mse['affect'] = st.selectbox(
                "Affect (Objective)",
                ['Appropriate', 'Inappropriate', 'Constricted', 'Blunted', 'Flat', 'Labile', 'Expansive', 'Reactive'],
                index=0 if not mse.get('affect') else ['Appropriate', 'Inappropriate', 'Constricted', 'Blunted', 'Flat', 'Labile', 'Expansive', 'Reactive'].index(mse.get('affect'))
            )
            
            mse['affect_intensity'] = st.selectbox(
                "Affect Intensity",
                ['Normal', 'Increased', 'Decreased', 'Absent'],
                index=0 if not mse.get('affect_intensity') else ['Normal', 'Increased', 'Decreased', 'Absent'].index(mse.get('affect_intensity'))
            )
        
        # Thought Process & Content
        st.markdown("### 💭 Thought Process & Content")
        col1, col2 = st.columns(2)
        with col1:
            mse['thought_process'] = st.multiselect(
                "Thought Process",
                ['Linear', 'Goal-directed', 'Tangential', 'Circumstantial', 'Flight of ideas', 'Loose associations', 'Thought blocking', 'Perseveration', 'Word salad'],
                default=mse.get('thought_process', [])
            )
            
            mse['thought_content_abnormal'] = st.multiselect(
                "Abnormal Thought Content",
                ['None', 'Delusions', 'Obsessions', 'Compulsions', 'Phobias', 'Suicidal ideation', 'Homicidal ideation', 'Paranoid thoughts'],
                default=mse.get('thought_content_abnormal', [])
            )
        
        with col2:
            if 'Delusions' in mse.get('thought_content_abnormal', []):
                mse['delusion_types'] = st.multiselect(
                    "Types of Delusions",
                    ['Persecutory', 'Grandiose', 'Somatic', 'Religious', 'Erotomanic', 'Jealous', 'Referential', 'Bizarre'],
                    default=mse.get('delusion_types', [])
                )
            
            mse['preoccupations'] = st.multiselect(
                "Preoccupations",
                ['None', 'Death', 'Guilt', 'Worthlessness', 'Somatic concerns', 'Financial worries', 'Relationship issues'],
                default=mse.get('preoccupations', [])
            )
        
        # Perceptions
        st.markdown("### 👁️ Perceptions")
        col1, col2 = st.columns(2)
        with col1:
            mse['hallucinations'] = st.multiselect(
                "Hallucinations",
                ['None', 'Auditory', 'Visual', 'Tactile', 'Olfactory', 'Gustatory', 'Command auditory'],
                default=mse.get('hallucinations', [])
            )
        
        with col2:
            mse['illusions'] = st.multiselect(
                "Illusions/Misperceptions",
                ['None', 'Visual illusions', 'Auditory illusions', 'Depersonalization', 'Derealization'],
                default=mse.get('illusions', [])
            )
        
        # Cognitive Assessment Quick Screen
        st.markdown("### 🧠 Cognitive Screen")
        col1, col2, col3 = st.columns(3)
        with col1:
            mse['orientation'] = st.selectbox(
                "Orientation",
                ['Oriented x3', 'Oriented x2', 'Oriented x1', 'Disoriented'],
                index=0 if not mse.get('orientation') else ['Oriented x3', 'Oriented x2', 'Oriented x1', 'Disoriented'].index(mse.get('orientation'))
            )
        
        with col2:
            mse['attention'] = st.selectbox(
                "Attention/Concentration",
                ['Normal', 'Mildly impaired', 'Moderately impaired', 'Severely impaired'],
                index=0 if not mse.get('attention') else ['Normal', 'Mildly impaired', 'Moderately impaired', 'Severely impaired'].index(mse.get('attention'))
            )
        
        with col3:
            mse['memory_immediate'] = st.selectbox(
                "Immediate Memory",
                ['Normal', 'Impaired'],
                index=0 if not mse.get('memory_immediate') else ['Normal', 'Impaired'].index(mse.get('memory_immediate'))
            )
        
        # Insight & Judgment
        st.markdown("### 🎯 Insight & Judgment")
        col1, col2 = st.columns(2)
        with col1:
            mse['insight'] = st.selectbox(
                "Insight",
                ['Good', 'Fair', 'Poor', 'Absent', 'Partial'],
                index=0 if not mse.get('insight') else ['Good', 'Fair', 'Poor', 'Absent', 'Partial'].index(mse.get('insight'))
            )
        
        with col2:
            mse['judgment'] = st.selectbox(
                "Judgment",
                ['Good', 'Fair', 'Poor', 'Impaired'],
                index=0 if not mse.get('judgment') else ['Good', 'Fair', 'Poor', 'Impaired'].index(mse.get('judgment'))
            )
        
        st.session_state.patient_data['mental_state_examination'] = mse

    # Section 9: Cognitive Assessment
    elif st.session_state.current_section == 9:
        st.markdown('<div class="section-header">Cognitive Assessment</div>', unsafe_allow_html=True)
        
        if 'cognitive_assessment' not in st.session_state.patient_data:
            st.session_state.patient_data['cognitive_assessment'] = {}
        
        cog = st.session_state.patient_data['cognitive_assessment']
        
        col1, col2 = st.columns(2)
        with col1:
            cog['orientation'] = st.selectbox(
                "Orientation",
                ['Oriented x3 (person, place, time)', 'Oriented x2', 'Oriented x1', 'Disoriented'],
                index=0 if not cog.get('orientation') else ['Oriented x3 (person, place, time)', 'Oriented x2', 'Oriented x1', 'Disoriented'].index(cog.get('orientation'))
            )
            
            cog['attention'] = st.selectbox(
                "Attention/Concentration",
                ['Normal', 'Mildly impaired', 'Moderately impaired', 'Severely impaired'],
                index=0 if not cog.get('attention') else ['Normal', 'Mildly impaired', 'Moderately impaired', 'Severely impaired'].index(cog.get('attention'))
            )
            
            cog['memory'] = st.selectbox(
                "Memory",
                ['Normal', 'Mildly impaired', 'Moderately impaired', 'Severely impaired'],
                index=0 if not cog.get('memory') else ['Normal', 'Mildly impaired', 'Moderately impaired', 'Severely impaired'].index(cog.get('memory'))
            )
        
        with col2:
            cog['insight'] = st.selectbox(
                "Insight",
                ['Good', 'Fair', 'Poor', 'Absent'],
                index=0 if not cog.get('insight') else ['Good', 'Fair', 'Poor', 'Absent'].index(cog.get('insight'))
            )
            
            cog['judgment'] = st.selectbox(
                "Judgment",
                ['Good', 'Fair', 'Poor', 'Impaired'],
                index=0 if not cog.get('judgment') else ['Good', 'Fair', 'Poor', 'Impaired'].index(cog.get('judgment'))
            )
            
            cog['abstract_thinking'] = st.selectbox(
                "Abstract Thinking",
                ['Normal', 'Concrete', 'Impaired'],
                index=0 if not cog.get('abstract_thinking') else ['Normal', 'Concrete', 'Impaired'].index(cog.get('abstract_thinking'))
            )
        
        st.session_state.patient_data['cognitive_assessment'] = cog

    # Section 10: Risk Assessment (with enhanced risk stratification)
    elif st.session_state.current_section == 10:
        st.markdown('<div class="section-header">Comprehensive Risk Assessment</div>', unsafe_allow_html=True)
        
        # Suicide Risk Assessment
        with st.expander("⚠️ Suicide Risk Assessment", expanded=True):
            st.markdown("**Current Suicidal Ideation:**")
            
            col1, col2 = st.columns(2)
            
            with col1:
                current_si = st.selectbox("Current Suicidal Ideation",
                                        ["None", "Passive (wish to be dead)", "Active ideation without plan",
                                         "Active ideation with plan", "Active ideation with intent"])
                
                # Conditional display based on suicidal ideation
                if current_si != "None":
                    si_frequency = st.selectbox("Frequency of Suicidal Thoughts",
                                              ["Rare", "Occasional", "Frequent", "Constant"])
                    si_intensity = st.select_slider("Intensity of Suicidal Thoughts", options=list(range(1, 11)), value=5)
                    
                    plan_details = st.text_area("Suicide Plan Details",
                                              placeholder="Method, location, timing, access to means...")
                    
                    protective_factors = st.multiselect("Protective Factors",
                                                      ["Family responsibilities", "Religious beliefs", "Future plans",
                                                       "Fear of death", "Treatment engagement", "Social support",
                                                       "Pets", "Other responsibilities"])
                else:
                    si_frequency = "Rare"
                    si_intensity = 1
                    plan_details = ""
                    protective_factors = []
            
            with col2:
                if current_si != "None":
                    means_access = st.selectbox("Access to Lethal Means",
                                              ["No access", "Limited access", "Easy access", "Immediate access"])
                    
                    deterrents = st.multiselect("What Stops Patient from Acting",
                                              ["Family", "Children", "Pets", "Religious beliefs", "Fear",
                                               "Treatment hope", "Responsibility to others"])
                    
                    si_triggers = st.text_area("Triggers for Suicidal Thoughts",
                                             placeholder="What makes suicidal thoughts worse?")
                else:
                    means_access = "No access"
                    deterrents = []
                    si_triggers = ""
            
            # Historical suicide risk factors
            st.markdown("**Historical Risk Factors:**")
            col1, col2 = st.columns(2)
            
            with col1:
                previous_attempts = st.selectbox("Previous Suicide Attempts",
                                               ["None", "1 attempt", "2-3 attempts", "> 3 attempts"])
                
                if previous_attempts != "None":
                    most_recent_attempt = st.date_input("Most Recent Attempt Date")
                    attempt_lethality = st.selectbox("Most Lethal Attempt",
                                                   ["Low lethality", "Moderate lethality", "High lethality", "Nearly fatal"])
                else:
                    most_recent_attempt = None
                    attempt_lethality = None
            
            with col2:
                family_suicide = st.selectbox("Family History of Suicide", ["No", "Yes - attempt", "Yes - completed"])
                abuse_history = st.selectbox("History of Abuse", ["None", "Physical", "Sexual", "Emotional", "Multiple types"])
            
            # Current risk level assessment
            st.markdown("**Current Suicide Risk Level:**")
            
            # Calculate risk level
            risk_data = {
                'suicide': {
                    'current_si': current_si,
                    'previous_attempts': previous_attempts,
                    'means_access': means_access if current_si != "None" else "No access",
                    'family_suicide': family_suicide
                }
            }
            
            suicide_risk_level = calculate_suicide_risk_level(risk_data)
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.selectbox("Overall Suicide Risk", 
                            ["Low", "Moderate", "High", "Imminent"],
                            index=["Low", "Moderate", "High", "Imminent"].index(suicide_risk_level),
                            disabled=True)
                
                if suicide_risk_level in ["High", "Imminent"]:
                    safety_plan_needed = st.selectbox("Safety Plan Created?", ["Yes", "No", "In progress"])
                    hospitalization_considered = st.selectbox("Hospitalization Considered?", 
                                                            ["Not needed", "Voluntary", "Involuntary", "Declined by patient"])
                else:
                    safety_plan_needed = "No"
                    hospitalization_considered = "Not needed"
            
            with col2:
                risk_factors_present = st.multiselect("Current Risk Factors Present",
                                                    ["Depression", "Hopelessness", "Isolation", "Substance use",
                                                     "Psychosis", "Impulsivity", "Recent loss", "Chronic pain",
                                                     "Financial stress", "Legal problems"])
        
        # Store risk assessment data
        risk_data = {
            'suicide': {
                'current_si': current_si, 'frequency': si_frequency if current_si != "None" else None,
                'intensity': si_intensity if current_si != "None" else None,
                'plan_details': plan_details if current_si != "None" else None,
                'means_access': means_access if current_si != "None" else None,
                'protective_factors': protective_factors if current_si != "None" else None,
                'deterrents': deterrents if current_si != "None" else None,
                'triggers': si_triggers if current_si != "None" else None,
                'previous_attempts': previous_attempts, 'family_suicide': family_suicide,
                'abuse_history': abuse_history, 'risk_level': suicide_risk_level,
                'risk_factors': risk_factors_present
            }
        }
        
        # Add conditional fields
        if previous_attempts != "None":
            risk_data['suicide']['most_recent_attempt'] = str(most_recent_attempt)
            risk_data['suicide']['attempt_lethality'] = attempt_lethality
        
        if suicide_risk_level in ["High", "Imminent"]:
            risk_data['suicide']['safety_plan'] = safety_plan_needed
            risk_data['suicide']['hospitalization'] = hospitalization_considered
        
        st.session_state.patient_data['risk_assessment'] = risk_data

    # Section 13: Diagnostic Formulation (with templated text)
    elif st.session_state.current_section == 13:
        st.markdown('<div class="section-header">Diagnostic Formulation</div>', unsafe_allow_html=True)
        
        # Primary diagnoses
        st.markdown('<div class="subsection-header">Primary Psychiatric Diagnoses</div>', unsafe_allow_html=True)
        
        col1, col2 = st.columns(2)
        
        with col1:
            primary_diagnosis = st.selectbox("Primary Diagnosis",
                                           ["Major Depressive Disorder", "Bipolar I Disorder", "Bipolar II Disorder",
                                            "Generalized Anxiety Disorder", "Panic Disorder", "Social Anxiety Disorder",
                                            "PTSD", "OCD", "ADHD", "Schizophrenia", "Schizoaffective Disorder",
                                            "Brief Psychotic Disorder", "Borderline Personality Disorder", 
                                            "Substance Use Disorder", "Adjustment Disorder", "Other", "Deferred"])
            
            primary_specifiers = st.multiselect("Primary Diagnosis Specifiers",
                                              ["Single episode", "Recurrent", "With psychotic features", "Severe",
                                               "With mixed features", "With rapid cycling", "Current episode manic",
                                               "Current episode depressed", "In remission", "Mild", "Moderate"])
            
            confidence_primary = st.select_slider("Diagnostic Confidence - Primary", 
                                                options=["Low", "Moderate", "High", "Very High"])
        
        with col2:
            secondary_diagnoses = st.multiselect("Secondary/Comorbid Diagnoses",
                                               ["Major Depressive Disorder", "Generalized Anxiety Disorder", "PTSD",
                                                "Substance Use Disorder", "Personality Disorder", "ADHD", "OCD",
                                                "Eating Disorder", "Sleep Disorder", "Other"])
            
            rule_out_diagnoses = st.multiselect("Rule Out/Differential Diagnoses",
                                              ["Bipolar Disorder", "Psychotic Disorder", "Personality Disorder",
                                               "Medical condition", "Substance-induced", "Adjustment Disorder"])
            
            diagnostic_certainty = st.selectbox("Overall Diagnostic Certainty",
                                              ["Provisional", "Working diagnosis", "Confident", "Definitive"])
        
        # Biopsychosocial formulation
        st.markdown('<div class="subsection-header">Biopsychosocial Formulation</div>', unsafe_allow_html=True)
        
        # Template buttons
        st.markdown("**Quick Templates:**")
        template_cols = st.columns(5)
        with template_cols[0]:
            if st.button("Depression", key="depression_template"):
                insert_template("depression", "biological_factors")
        with template_cols[1]:
            if st.button("Anxiety", key="anxiety_template"):
                insert_template("anxiety", "psychological_factors")
        with template_cols[2]:
            if st.button("Psychosis", key="psychosis_template"):
                insert_template("psychosis", "social_factors")
        with template_cols[3]:
            if st.button("Substance", key="substance_template"):
                insert_template("substance", "precipitating_factors")
        with template_cols[4]:
            if st.button("Trauma", key="trauma_template"):
                insert_template("trauma", "case_summary")
        
        col1, col2 = st.columns(2)
        
        with col1:
            biological_factors = st.text_area("Biological Factors",
                                            placeholder="Genetic predisposition, medical conditions, medication effects, substance use...",
                                            height=120, key="biological_factors")
            
            psychological_factors = st.text_area("Psychological Factors", 
                                               placeholder="Personality traits, coping mechanisms, cognitive patterns, trauma history...",
                                               height=120, key="psychological_factors")
        
        with col2:
            social_factors = st.text_area("Social Factors",
                                        placeholder="Family dynamics, social support, cultural factors, socioeconomic status...",
                                        height=120, key="social_factors")
            
            precipitating_factors = st.text_area("Precipitating Factors",
                                               placeholder="Recent events that triggered current episode...",
                                               height=120, key="precipitating_factors")
        
        # Case formulation summary
        st.markdown('<div class="subsection-header">Case Formulation Summary</div>', unsafe_allow_html=True)
        
        case_summary = st.text_area("Integrated Case Formulation",
                                   placeholder="Provide a comprehensive summary integrating all biopsychosocial factors...",
                                   height=200, key="case_summary")
        
        # Store diagnostic formulation data
        diagnostic_data = {
            'diagnoses': {
                'primary': primary_diagnosis, 'primary_specifiers': primary_specifiers,
                'confidence_primary': confidence_primary, 'secondary': secondary_diagnoses,
                'rule_out': rule_out_diagnoses, 'certainty': diagnostic_certainty
            },
            'biopsychosocial': {
                'biological': biological_factors, 'psychological': psychological_factors,
                'social': social_factors, 'precipitating': precipitating_factors
            },
            'case_summary': case_summary
        }
        
        st.session_state.patient_data['diagnostic_formulation'] = diagnostic_data

    # Section 11: Laboratory & Investigations
    elif st.session_state.current_section == 11:
        st.markdown('<div class="section-header">Laboratory & Investigations</div>', unsafe_allow_html=True)
        
        if 'laboratory_investigations' not in st.session_state.patient_data:
            st.session_state.patient_data['laboratory_investigations'] = {}
        
        lab = st.session_state.patient_data['laboratory_investigations']
        
        # Complete Blood Count (CBC)
        st.markdown("### 🩸 Complete Blood Count (CBC)")
        col1, col2, col3 = st.columns(3)
        
        if 'cbc' not in lab:
            lab['cbc'] = {}
        
        with col1:
            lab['cbc']['wbc'] = st.number_input("WBC (4.0-11.0 K/μL)", 
                                               value=lab['cbc'].get('wbc') if lab['cbc'].get('wbc') else None, 
                                               min_value=0.0, max_value=50.0, step=0.1)
            lab['cbc']['rbc'] = st.number_input("RBC (4.2-5.4 M/μL)", 
                                               value=lab['cbc'].get('rbc') if lab['cbc'].get('rbc') else None, 
                                               min_value=0.0, max_value=10.0, step=0.1)
            lab['cbc']['hgb'] = st.number_input("Hemoglobin (12-16 g/dL)", 
                                               value=lab['cbc'].get('hgb') if lab['cbc'].get('hgb') else None, 
                                               min_value=0.0, max_value=25.0, step=0.1)
        
        with col2:
            lab['cbc']['hct'] = st.number_input("Hematocrit (36-46%)", 
                                               value=lab['cbc'].get('hct') if lab['cbc'].get('hct') else None, 
                                               min_value=0.0, max_value=100.0, step=0.1)
            lab['cbc']['plt'] = st.number_input("Platelets (150-450 K/μL)", 
                                               value=lab['cbc'].get('plt') if lab['cbc'].get('plt') else None, 
                                               min_value=0.0, max_value=1000.0, step=1.0)
            lab['cbc']['mcv'] = st.number_input("MCV (80-100 fL)", 
                                               value=lab['cbc'].get('mcv') if lab['cbc'].get('mcv') else None, 
                                               min_value=0.0, max_value=150.0, step=0.1)
        
        with col3:
            lab['cbc']['neutrophils'] = st.number_input("Neutrophils (%)", 
                                                       value=lab['cbc'].get('neutrophils', 0.0), 
                                                       min_value=0.0, max_value=100.0, step=0.1)
            lab['cbc']['lymphocytes'] = st.number_input("Lymphocytes (%)", 
                                                       value=lab['cbc'].get('lymphocytes', 0.0), 
                                                       min_value=0.0, max_value=100.0, step=0.1)
            lab['cbc']['monocytes'] = st.number_input("Monocytes (%)", 
                                                     value=lab['cbc'].get('monocytes', 0.0), 
                                                     min_value=0.0, max_value=100.0, step=0.1)
        
        # Basic Metabolic Panel (BMP)
        st.markdown("### ⚗️ Basic Metabolic Panel (BMP)")
        col1, col2, col3 = st.columns(3)
        
        if 'bmp' not in lab:
            lab['bmp'] = {}
        
        with col1:
            lab['bmp']['glucose'] = st.number_input("Glucose (70-100 mg/dL)", 
                                                   value=lab['bmp'].get('glucose', 0.0), 
                                                   min_value=0.0, max_value=500.0, step=1.0)
            lab['bmp']['bun'] = st.number_input("BUN (7-20 mg/dL)", 
                                               value=lab['bmp'].get('bun', 0.0), 
                                               min_value=0.0, max_value=200.0, step=1.0)
            lab['bmp']['creatinine'] = st.number_input("Creatinine (0.6-1.2 mg/dL)", 
                                                      value=lab['bmp'].get('creatinine', 0.0), 
                                                      min_value=0.0, max_value=20.0, step=0.1)
        
        with col2:
            lab['bmp']['sodium'] = st.number_input("Sodium (136-145 mEq/L)", 
                                                  value=lab['bmp'].get('sodium', 0.0), 
                                                  min_value=0.0, max_value=200.0, step=1.0)
            lab['bmp']['potassium'] = st.number_input("Potassium (3.5-5.0 mEq/L)", 
                                                     value=lab['bmp'].get('potassium', 0.0), 
                                                     min_value=0.0, max_value=10.0, step=0.1)
            lab['bmp']['chloride'] = st.number_input("Chloride (98-107 mEq/L)", 
                                                    value=lab['bmp'].get('chloride', 0.0), 
                                                    min_value=0.0, max_value=200.0, step=1.0)
        
        with col3:
            lab['bmp']['co2'] = st.number_input("CO2 (22-28 mEq/L)", 
                                               value=lab['bmp'].get('co2', 0.0), 
                                               min_value=0.0, max_value=50.0, step=1.0)
            lab['bmp']['egfr'] = st.number_input("eGFR (>60 mL/min/1.73m²)", 
                                                value=lab['bmp'].get('egfr', 0.0), 
                                                min_value=0.0, max_value=200.0, step=1.0)
        
        # Liver Function Tests
        st.markdown("### 🫀 Liver Function Tests")
        col1, col2 = st.columns(2)
        
        if 'lft' not in lab:
            lab['lft'] = {}
        
        with col1:
            lab['lft']['alt'] = st.number_input("ALT (7-56 U/L)", 
                                               value=lab['lft'].get('alt', 0.0), 
                                               min_value=0.0, max_value=1000.0, step=1.0)
            lab['lft']['ast'] = st.number_input("AST (10-40 U/L)", 
                                               value=lab['lft'].get('ast', 0.0), 
                                               min_value=0.0, max_value=1000.0, step=1.0)
            lab['lft']['bilirubin_total'] = st.number_input("Total Bilirubin (0.3-1.2 mg/dL)", 
                                                           value=lab['lft'].get('bilirubin_total', 0.0), 
                                                           min_value=0.0, max_value=50.0, step=0.1)
        
        with col2:
            lab['lft']['alkaline_phosphatase'] = st.number_input("Alkaline Phosphatase (44-147 U/L)", 
                                                               value=lab['lft'].get('alkaline_phosphatase', 0.0), 
                                                               min_value=0.0, max_value=1000.0, step=1.0)
            lab['lft']['albumin'] = st.number_input("Albumin (3.5-5.0 g/dL)", 
                                                   value=lab['lft'].get('albumin', 0.0), 
                                                   min_value=0.0, max_value=10.0, step=0.1)
            lab['lft']['protein_total'] = st.number_input("Total Protein (6.0-8.3 g/dL)", 
                                                         value=lab['lft'].get('protein_total', 0.0), 
                                                         min_value=0.0, max_value=15.0, step=0.1)
        
        # Thyroid Function
        st.markdown("### 🦋 Thyroid Function")
        col1, col2 = st.columns(2)
        
        if 'thyroid' not in lab:
            lab['thyroid'] = {}
        
        with col1:
            lab['thyroid']['tsh'] = st.number_input("TSH (0.4-4.0 mIU/L)", 
                                                   value=lab['thyroid'].get('tsh', 0.0), 
                                                   min_value=0.0, max_value=100.0, step=0.1)
            lab['thyroid']['t4_free'] = st.number_input("Free T4 (0.8-1.8 ng/dL)", 
                                                       value=lab['thyroid'].get('t4_free', 0.0), 
                                                       min_value=0.0, max_value=10.0, step=0.1)
        
        with col2:
            lab['thyroid']['t3_free'] = st.number_input("Free T3 (2.3-4.2 pg/mL)", 
                                                       value=lab['thyroid'].get('t3_free', 0.0), 
                                                       min_value=0.0, max_value=20.0, step=0.1)
        
        # Vitamins & Other
        st.markdown("### 💊 Vitamins & Other Tests")
        col1, col2 = st.columns(2)
        
        if 'vitamins' not in lab:
            lab['vitamins'] = {}
        
        with col1:
            lab['vitamins']['b12'] = st.number_input("Vitamin B12 (200-900 pg/mL)", 
                                                    value=lab['vitamins'].get('b12', 0.0), 
                                                    min_value=0.0, max_value=2000.0, step=10.0)
            lab['vitamins']['folate'] = st.number_input("Folate (>3.0 ng/mL)", 
                                                       value=lab['vitamins'].get('folate', 0.0), 
                                                       min_value=0.0, max_value=50.0, step=0.1)
            lab['vitamins']['vitamin_d'] = st.number_input("Vitamin D (30-100 ng/mL)", 
                                                          value=lab['vitamins'].get('vitamin_d', 0.0), 
                                                          min_value=0.0, max_value=200.0, step=1.0)
        
        with col2:
            lab['vitamins']['hba1c'] = st.number_input("HbA1c (%)", 
                                                      value=lab['vitamins'].get('hba1c', 0.0), 
                                                      min_value=0.0, max_value=20.0, step=0.1)
            lab['vitamins']['magnesium'] = st.number_input("Magnesium (1.7-2.2 mg/dL)", 
                                                          value=lab['vitamins'].get('magnesium', 0.0), 
                                                          min_value=0.0, max_value=10.0, step=0.1)
        
        # Imaging Studies
        st.markdown("### 🔬 Imaging Studies")
        if 'imaging' not in lab:
            lab['imaging'] = {}
        
        lab['imaging']['studies_ordered'] = st.multiselect(
            "Imaging Studies Ordered",
            ["CT Head", "MRI Brain", "Chest X-ray", "CT Chest", "CT Abdomen/Pelvis", "Ultrasound", "PET Scan", "SPECT", "Other"],
            default=lab['imaging'].get('studies_ordered', [])
        )
        
        if lab['imaging']['studies_ordered']:
            lab['imaging']['results'] = st.text_area(
                "Imaging Results Summary",
                value=lab['imaging'].get('results', ''),
                height=100
            )
        
        # Other Tests
        st.markdown("### 🧪 Other Specialized Tests")
        if 'other_tests' not in lab:
            lab['other_tests'] = {}
        
        lab['other_tests']['tests_ordered'] = st.multiselect(
            "Other Tests Ordered",
            ["EEG", "Neuropsychological Testing", "Drug Screen", "Alcohol Level", "Lithium Level", "Valproic Acid Level", "Carbamazepine Level", "Other Medication Levels"],
            default=lab['other_tests'].get('tests_ordered', [])
        )
        
        if lab['other_tests']['tests_ordered']:
            lab['other_tests']['results'] = st.text_area(
                "Other Test Results",
                value=lab['other_tests'].get('results', ''),
                height=80
            )
        
        st.session_state.patient_data['laboratory_investigations'] = lab

    # Section 12: Clinical Scales & Ratings
    elif st.session_state.current_section == 12:
        st.markdown('<div class="section-header">Clinical Scales & Ratings</div>', unsafe_allow_html=True)
        
        if 'clinical_scales' not in st.session_state.patient_data:
            st.session_state.patient_data['clinical_scales'] = {}
        
        scales = st.session_state.patient_data['clinical_scales']
        
        # PHQ-9 Depression Scale
        st.markdown("### PHQ-9 Depression Scale")
        if 'depression' not in scales:
            scales['depression'] = {}
        
        phq9_questions = [
            "Little interest or pleasure in doing things",
            "Feeling down, depressed, or hopeless", 
            "Trouble falling or staying asleep, or sleeping too much",
            "Feeling tired or having little energy",
            "Poor appetite or overeating",
            "Feeling bad about yourself or that you are a failure",
            "Trouble concentrating on things",
            "Moving or speaking slowly or being fidgety/restless",
            "Thoughts that you would be better off dead or hurting yourself"
        ]
        
        phq9_total = 0
        for i, question in enumerate(phq9_questions):
            score = st.selectbox(
                f"PHQ-9 Q{i+1}: {question}",
                ["Not at all (0)", "Several days (1)", "More than half the days (2)", "Nearly every day (3)"],
                index=scales['depression'].get(f'q{i+1}', 0),
                key=f"phq9_q{i+1}"
            )
            try:
                score_value = int(score.split('(')[1].split(')')[0])
                # Validate score range
                if 0 <= score_value <= 3:
                    scales['depression'][f'q{i+1}'] = score_value
                    phq9_total += score_value
                else:
                    st.error(f"Invalid PHQ-9 score: {score_value}. Must be between 0 and 3.")
                    scales['depression'][f'q{i+1}'] = 0
            except (ValueError, IndexError) as e:
                st.error(f"Error parsing PHQ-9 score for question {i+1}: {e}")
                scales['depression'][f'q{i+1}'] = 0

        # Validate total score
        if 0 <= phq9_total <= 27:
            scales['depression']['phq9_total'] = phq9_total
            st.info(f"PHQ-9 Total Score: {phq9_total}/27")
        else:
            st.error(f"Invalid PHQ-9 total score: {phq9_total}. Must be between 0 and 27.")
            scales['depression']['phq9_total'] = 0
        
        # GAD-7 Anxiety Scale
        st.markdown("### GAD-7 Anxiety Scale")
        if 'anxiety' not in scales:
            scales['anxiety'] = {}
        
        gad7_questions = [
            "Feeling nervous, anxious, or on edge",
            "Not being able to stop or control worrying",
            "Worrying too much about different things", 
            "Trouble relaxing",
            "Being so restless that it is hard to sit still",
            "Becoming easily annoyed or irritable",
            "Feeling afraid, as if something awful might happen"
        ]
        
        gad7_total = 0
        for i, question in enumerate(gad7_questions):
            score = st.selectbox(
                f"GAD-7 Q{i+1}: {question}",
                ["Not at all (0)", "Several days (1)", "More than half the days (2)", "Nearly every day (3)"],
                index=scales['anxiety'].get(f'q{i+1}', 0),
                key=f"gad7_q{i+1}"
            )
            try:
                score_value = int(score.split('(')[1].split(')')[0])
                # Validate score range
                if 0 <= score_value <= 3:
                    scales['anxiety'][f'q{i+1}'] = score_value
                    gad7_total += score_value
                else:
                    st.error(f"Invalid GAD-7 score: {score_value}. Must be between 0 and 3.")
                    scales['anxiety'][f'q{i+1}'] = 0
            except (ValueError, IndexError) as e:
                st.error(f"Error parsing GAD-7 score for question {i+1}: {e}")
                scales['anxiety'][f'q{i+1}'] = 0

        # Validate total score
        if 0 <= gad7_total <= 21:
            scales['anxiety']['gad7_total'] = gad7_total
            st.info(f"GAD-7 Total Score: {gad7_total}/21")
        else:
            st.error(f"Invalid GAD-7 total score: {gad7_total}. Must be between 0 and 21.")
            scales['anxiety']['gad7_total'] = 0
        
        st.session_state.patient_data['clinical_scales'] = scales

    # Section 14: Treatment Planning
    elif st.session_state.current_section == 14:
        st.markdown('<div class="section-header">Treatment Planning</div>', unsafe_allow_html=True)
        
        if 'treatment_planning' not in st.session_state.patient_data:
            st.session_state.patient_data['treatment_planning'] = {}
        
        treatment = st.session_state.patient_data['treatment_planning']
        
        col1, col2 = st.columns(2)
        with col1:
            treatment['immediate_interventions'] = st.text_area(
                "Immediate Interventions",
                value=treatment.get('immediate_interventions', ''),
                height=100,
                help="Crisis interventions, safety planning, etc."
            )
            
            treatment['medication_recommendations'] = st.text_area(
                "Medication Recommendations",
                value=treatment.get('medication_recommendations', ''),
                height=100
            )
            
            treatment['therapy_recommendations'] = st.text_area(
                "Therapy Recommendations",
                value=treatment.get('therapy_recommendations', ''),
                height=100,
                help="CBT, DBT, psychodynamic, etc."
            )
        
        with col2:
            treatment['goals'] = st.text_area(
                "Treatment Goals",
                value=treatment.get('goals', ''),
                height=100,
                help="Short-term and long-term goals"
            )
            
            treatment['referrals'] = st.text_area(
                "Referrals",
                value=treatment.get('referrals', ''),
                height=100,
                help="Specialists, programs, services"
            )
            
            treatment['patient_preferences'] = st.text_area(
                "Patient Preferences",
                value=treatment.get('patient_preferences', ''),
                height=100
            )
        
        st.session_state.patient_data['treatment_planning'] = treatment

    # Section 15: Follow-up & Monitoring
    elif st.session_state.current_section == 15:
        st.markdown('<div class="section-header">Follow-up & Monitoring</div>', unsafe_allow_html=True)
        
        if 'follow_up_monitoring' not in st.session_state.patient_data:
            st.session_state.patient_data['follow_up_monitoring'] = {}
        
        followup = st.session_state.patient_data['follow_up_monitoring']
        
        col1, col2 = st.columns(2)
        with col1:
            followup['next_appointment'] = st.date_input(
                "Next Appointment Date",
                value=followup.get('next_appointment', datetime.date.today() + datetime.timedelta(days=14))
            )
            
            followup['frequency'] = st.selectbox(
                "Follow-up Frequency",
                ["Weekly", "Bi-weekly", "Monthly", "As needed", "Other"],
                index=0 if not followup.get('frequency') else ["Weekly", "Bi-weekly", "Monthly", "As needed", "Other"].index(followup.get('frequency'))
            )
            
            followup['monitoring_parameters'] = st.text_area(
                "Monitoring Parameters",
                value=followup.get('monitoring_parameters', ''),
                height=100,
                help="What to monitor (symptoms, side effects, etc.)"
            )
        
        with col2:
            followup['warning_signs'] = st.text_area(
                "Warning Signs to Watch For",
                value=followup.get('warning_signs', ''),
                height=100
            )
            
            followup['emergency_contacts'] = st.text_area(
                "Emergency Contacts",
                value=followup.get('emergency_contacts', ''),
                height=100,
                help="Crisis hotlines, emergency contacts"
            )
            
            followup['patient_education'] = st.text_area(
                "Patient Education Provided",
                value=followup.get('patient_education', ''),
                height=100
            )
        
        st.session_state.patient_data['follow_up_monitoring'] = followup

    # Navigation and action buttons (with improved reset functionality)
    st.markdown("---")
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        if st.button("⬅️ Previous Section") and st.session_state.current_section > 0:
            st.session_state.current_section -= 1
            st.rerun()
    with col2:
        if st.button("💾 Save Progress"):
            save_current_assessment()
    with col3:
        if st.button("➡️ Next Section") and st.session_state.current_section < len(sections) - 1:
            st.session_state.current_section += 1
            st.rerun()
    with col4:
        # Improved reset functionality
        if st.button("🔄 Reset Assessment"):
            st.session_state.show_reset_confirm = True

    # Reset confirmation dialog
    if st.session_state.get('show_reset_confirm', False):
        st.warning("⚠️ Are you sure you want to reset the current assessment? All unsaved data will be lost.")
        col1, col2 = st.columns(2)
        with col1:
            if st.button("Yes, Reset"):
                st.session_state.patient_data = {}
                st.session_state.current_section = 0
                st.session_state.patient_id = None  # Clear patient code
                st.session_state.editing_assessment_id = None
                st.session_state.show_reset_confirm = False
                st.session_state.patient_code_entered = False
                st.rerun()
        with col2:
            if st.button("Cancel"):
                st.session_state.show_reset_confirm = False
                st.rerun()

    # Auto-save functionality
    auto_save_progress()

def show_dashboard_view():
    """Display the assessment dashboard"""
    st.markdown('<h1 class="main-header">📊 Psychiatric Assessment Dashboard</h1>', unsafe_allow_html=True)
    
    # Dashboard navigation
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        if st.button("🧠 New Assessment"):
            new_assessment()
    with col2:
        if st.button("🔄 Refresh Data"):
            st.rerun()
    with col3:
        if st.button("📋 Validate Database"):
            valid, message = validate_database()
            if valid:
                st.success(message)
            else:
                st.error(message)
    with col4:
        if st.button("📥 Export ML Dataset"):
            ml_data = get_ml_dataset()
            if not ml_data.empty:
                csv = ml_data.to_csv(index=False)
                st.download_button(
                    label="Download ML Dataset",
                    data=csv,
                    file_name=f"psychiatric_ml_dataset_{datetime.datetime.now().strftime('%Y%m%d')}.csv",
                    mime="text/csv"
                )
            else:
                st.warning("No data available for export")
    
    # Search and filter
    st.markdown("### 🔍 Search and Filter Assessments")
    
    col1, col2, col3 = st.columns(3)
    with col1:
        search_term = st.text_input("Search by Patient ID", placeholder="Enter patient ID...")
    with col2:
        diagnosis_filter = st.multiselect("Filter by Diagnosis", 
                                         ["Major Depressive Disorder", "Bipolar I Disorder", "Bipolar II Disorder",
                                          "Generalized Anxiety Disorder", "PTSD", "OCD", "ADHD", "Schizophrenia",
                                          "Substance Use Disorder", "Other"])
    with col3:
        risk_filter = st.multiselect("Filter by Risk Level", ["Low", "Moderate", "High", "Imminent"])
    
    # Get all patients
    patients_df = get_all_patients()
    
    if not patients_df.empty:
        # Apply filters
        if search_term:
            patients_df = patients_df[patients_df['patient_id'].str.contains(search_term, case=False, na=False)]
        
        # Get assessments for filtered patients
        if not patients_df.empty:
            patient_ids = patients_df['patient_id'].tolist()
            all_assessments = []
            
            for patient_id in patient_ids:
                assessments = get_patient_assessments(patient_id)
                if not assessments.empty:
                    all_assessments.append(assessments)
            
            if all_assessments:
                assessments_df = pd.concat(all_assessments)
                
                # Apply additional filters
                if diagnosis_filter:
                    assessments_df = assessments_df[assessments_df['primary_diagnosis'].isin(diagnosis_filter)]
                
                if risk_filter:
                    assessments_df = assessments_df[assessments_df['suicide_risk_level'].isin(risk_filter)]
                
                # Display metrics
                st.markdown("### 📈 Assessment Metrics")
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    st.metric("Total Patients", len(patients_df))
                with col2:
                    st.metric("Total Assessments", len(assessments_df))
                with col3:
                    avg_completion = assessments_df['completion_percentage'].mean() if not assessments_df.empty else 0
                    st.metric("Avg. Completion", f"{avg_completion:.1f}%")
                with col4:
                    high_risk_count = len(assessments_df[assessments_df['suicide_risk_level'].isin(['High', 'Imminent'])])
                    st.metric("High Risk Cases", high_risk_count)
                
                # Display charts
                st.markdown("### 📊 Data Visualization")
                
                col1, col2 = st.columns(2)
                
                with col1:
                    # Diagnosis distribution
                    if not assessments_df.empty:
                        diagnosis_counts = assessments_df['primary_diagnosis'].value_counts().reset_index()
                        diagnosis_counts.columns = ['Diagnosis', 'Count']
                        
                        fig_diagnosis = px.pie(
                            diagnosis_counts, 
                            values='Count', 
                            names='Diagnosis',
                            title="Primary Diagnosis Distribution",
                            hole=0.3
                        )
                        fig_diagnosis.update_traces(textposition='inside', textinfo='percent+label')
                        st.plotly_chart(fig_diagnosis, use_container_width=True)
                
                with col2:
                    # Risk level distribution
                    if not assessments_df.empty:
                        risk_counts = assessments_df['suicide_risk_level'].value_counts().reset_index()
                        risk_counts.columns = ['Risk Level', 'Count']
                        
                        # Order risk levels
                        risk_order = ['Low', 'Moderate', 'High', 'Imminent']
                        risk_counts['Risk Level'] = pd.Categorical(risk_counts['Risk Level'], categories=risk_order, ordered=True)
                        risk_counts = risk_counts.sort_values('Risk Level')
                        
                        fig_risk = px.bar(
                            risk_counts,
                            x='Risk Level',
                            y='Count',
                            title="Suicide Risk Level Distribution",
                            color='Risk Level',
                            color_discrete_map={
                                'Low': '#10b981',
                                'Moderate': '#f59e0b',
                                'High': '#ef4444',
                                'Imminent': '#dc2626'
                            }
                        )
                        st.plotly_chart(fig_risk, use_container_width=True)
                
                # Display patient assessments
                st.markdown("### 📋 Patient Assessments")
                
                # Group by patient
                assessments_df['assessment_date'] = pd.to_datetime(assessments_df['assessment_date'])
                assessments_df = assessments_df.sort_values('assessment_date', ascending=False)
                
                for _, row in assessments_df.iterrows():
                    patient_id = row['patient_id']
                    patient_data = patients_df[patients_df['patient_id'] == patient_id].iloc[0] if not patients_df[patients_df['patient_id'] == patient_id].empty else None
                    
                    if patient_data is not None:
                        with st.expander(f"📝 {patient_id} - {row['assessment_date'].strftime('%Y-%m-%d %H:%M')}"):
                            col1, col2, col3 = st.columns(3)
                            
                            with col1:
                                st.markdown(f"**Age:** {patient_data['age'] if pd.notna(patient_data['age']) else 'Not specified'}")
                                st.markdown(f"**Gender:** {patient_data['gender']}")
                                st.markdown(f"**Primary Diagnosis:** {row['primary_diagnosis'] if pd.notna(row['primary_diagnosis']) else 'Not specified'}")
                            
                            with col2:
                                st.markdown(f"**Risk Level:** <span class='risk-{row['suicide_risk_level'].lower()}'>{row['suicide_risk_level']}</span>", unsafe_allow_html=True)
                                if pd.notna(row['phq9_score']):
                                    st.markdown(f"**PHQ-9 Score:** {row['phq9_score']}")
                                if pd.notna(row['gad7_score']):
                                    st.markdown(f"**GAD-7 Score:** {row['gad7_score']}")
                            
                            with col3:
                                st.markdown(f"**Completion:** {row['completion_percentage']:.1f}%")
                                st.markdown(f"**Duration:** {row['duration_minutes']:.1f} minutes")
                                
                                # Action buttons
                                col1, col2 = st.columns(2)
                                with col1:
                                    if st.button("Edit", key=f"edit_{row['assessment_id']}"):
                                        load_assessment(row['assessment_id'])
                                with col2:
                                    if st.button("Delete", key=f"delete_{row['assessment_id']}"):
                                        delete_assessment(row['assessment_id'])
                                        st.rerun()
            else:
                st.warning("No assessments found for the filtered patients.")
        else:
            st.warning("No patients found matching your search criteria.")
    else:
        st.warning("No patient data available. Please create an assessment first.")

# Main app logic
if st.session_state.current_view == 'assessment':
    show_assessment_view()
else:
    show_dashboard_view()